{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.header`\n  background-color: #ffffff;\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n`;\n_c = HeaderContainer;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c2 = Logo;\nconst LogoIcon = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 18px;\n`;\n_c3 = LogoIcon;\nconst LogoText = styled.span`\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n`;\n_c4 = LogoText;\nconst LanguageSelector = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n`;\n_c5 = LanguageSelector;\nconst Globe = styled.span`\n  font-size: 16px;\n`;\n_c6 = Globe;\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Logo, {\n      children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n        children: \"S\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        children: \"scottsdalegolf Mall\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LanguageSelector, {\n      children: [/*#__PURE__*/_jsxDEV(Globe, {\n        children: \"\\uD83C\\uDF10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"English\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c7 = Header;\nexport default Header;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"Logo\");\n$RefreshReg$(_c3, \"LogoIcon\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"LanguageSelector\");\n$RefreshReg$(_c6, \"Globe\");\n$RefreshReg$(_c7, \"Header\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_c", "Logo", "div", "_c2", "LogoIcon", "_c3", "LogoText", "span", "_c4", "LanguageSelector", "_c5", "Globe", "_c6", "Header", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst HeaderContainer = styled.header`\n  background-color: #ffffff;\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst LogoIcon = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 18px;\n`;\n\nconst LogoText = styled.span`\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n`;\n\nconst LanguageSelector = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n`;\n\nconst Globe = styled.span`\n  font-size: 16px;\n`;\n\nconst Header = () => {\n  return (\n    <HeaderContainer>\n      <Logo>\n        <LogoIcon>S</LogoIcon>\n        <LogoText>scottsdalegolf Mall</LogoText>\n      </Logo>\n      <LanguageSelector>\n        <Globe>🌐</Globe>\n        <span>English</span>\n      </LanguageSelector>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAVIF,eAAe;AAYrB,MAAMG,IAAI,GAAGN,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,QAAQ,GAAGT,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,QAAQ;AAad,MAAME,QAAQ,GAAGX,MAAM,CAACY,IAAI;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,gBAAgB,GAAGd,MAAM,CAACO,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAPID,gBAAgB;AAStB,MAAME,KAAK,GAAGhB,MAAM,CAACY,IAAI;AACzB;AACA,CAAC;AAACK,GAAA,GAFID,KAAK;AAIX,MAAME,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACEhB,OAAA,CAACC,eAAe;IAAAgB,QAAA,gBACdjB,OAAA,CAACI,IAAI;MAAAa,QAAA,gBACHjB,OAAA,CAACO,QAAQ;QAAAU,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACtBrB,OAAA,CAACS,QAAQ;QAAAQ,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACPrB,OAAA,CAACY,gBAAgB;MAAAK,QAAA,gBACfjB,OAAA,CAACc,KAAK;QAAAG,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjBrB,OAAA;QAAAiB,QAAA,EAAM;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAACC,GAAA,GAbIN,MAAM;AAeZ,eAAeA,MAAM;AAAC,IAAAb,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}