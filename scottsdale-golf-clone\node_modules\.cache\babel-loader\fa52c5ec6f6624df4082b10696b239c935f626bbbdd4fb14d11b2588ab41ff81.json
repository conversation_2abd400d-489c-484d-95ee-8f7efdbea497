{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\n\n/**\r\n * Actions represent the type of change to a location value.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#action\r\n */\nvar Action;\n(function (Action) {\n  /**\r\n   * A POP indicates a change to an arbitrary index in the history stack, such\r\n   * as a back or forward navigation. It does not describe the direction of the\r\n   * navigation, only that the current index changed.\r\n   *\r\n   * Note: This is the default action for newly created history objects.\r\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\r\n   * A PUSH indicates a new entry being added to the history stack, such as when\r\n   * a link is clicked and a new page loads. When this happens, all subsequent\r\n   * entries in the stack are lost.\r\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\r\n   * A REPLACE indicates the entry at the current index in the history stack\r\n   * being replaced by a new one.\r\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nvar readOnly = process.env.NODE_ENV !== \"production\" ? function (obj) {\n  return Object.freeze(obj);\n} : function (obj) {\n  return obj;\n};\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== 'undefined') console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nvar BeforeUnloadEventType = 'beforeunload';\nvar HashChangeEventType = 'hashchange';\nvar PopStateEventType = 'popstate';\n/**\r\n * Browser history stores the location in regular URLs. This is the standard for\r\n * most web apps, but it requires some configuration on the server to ensure you\r\n * serve the same app at multiple URLs.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\r\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    _options$window = _options.window,\n    window = _options$window === void 0 ? document.defaultView : _options$window;\n  var globalHistory = window.history;\n  function getIndexAndLocation() {\n    var _window$location = window.location,\n      pathname = _window$location.pathname,\n      search = _window$location.search,\n      hash = _window$location.hash;\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n  var blockedPopTx = null;\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n      var _getIndexAndLocation = getIndexAndLocation(),\n        nextIndex = _getIndexAndLocation[0],\n        nextLocation = _getIndexAndLocation[1];\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false,\n          // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better what\n          // is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n  window.addEventListener(PopStateEventType, handlePop);\n  var action = Action.Pop;\n  var _getIndexAndLocation2 = getIndexAndLocation(),\n    index = _getIndexAndLocation2[0],\n    location = _getIndexAndLocation2[1];\n  var listeners = createEvents();\n  var blockers = createEvents();\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  } // state defaults to `null` because `window.history.state` does\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n  function applyTx(nextAction) {\n    action = nextAction;\n    var _getIndexAndLocation3 = getIndexAndLocation();\n    index = _getIndexAndLocation3[0];\n    location = _getIndexAndLocation3[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      push(to, state);\n    }\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr = getHistoryStateAndUrl(nextLocation, index + 1),\n        historyState = _getHistoryStateAndUr[0],\n        url = _getHistoryStateAndUr[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n      applyTx(nextAction);\n    }\n  }\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      replace(to, state);\n    }\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr2 = getHistoryStateAndUrl(nextLocation, index),\n        historyState = _getHistoryStateAndUr2[0],\n        url = _getHistoryStateAndUr2[1]; // TODO: Support forced reloading\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n  var history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return location;\n    },\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Hash history stores the location in window.location.hash. This makes it ideal\r\n * for situations where you don't want to send the location to the server for\r\n * some reason, either because you do cannot configure it or the URL space is\r\n * reserved for something else.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\r\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options2 = options,\n    _options2$window = _options2.window,\n    window = _options2$window === void 0 ? document.defaultView : _options2$window;\n  var globalHistory = window.history;\n  function getIndexAndLocation() {\n    var _parsePath = parsePath(window.location.hash.substr(1)),\n      _parsePath$pathname = _parsePath.pathname,\n      pathname = _parsePath$pathname === void 0 ? '/' : _parsePath$pathname,\n      _parsePath$search = _parsePath.search,\n      search = _parsePath$search === void 0 ? '' : _parsePath$search,\n      _parsePath$hash = _parsePath.hash,\n      hash = _parsePath$hash === void 0 ? '' : _parsePath$hash;\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n  var blockedPopTx = null;\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n      var _getIndexAndLocation4 = getIndexAndLocation(),\n        nextIndex = _getIndexAndLocation4[0],\n        nextLocation = _getIndexAndLocation4[1];\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false,\n          // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better\n          // what is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n  window.addEventListener(PopStateEventType, handlePop); // popstate does not fire on hashchange in IE 11 and old (trident) Edge\n  // https://developer.mozilla.org/de/docs/Web/API/Window/popstate_event\n\n  window.addEventListener(HashChangeEventType, function () {\n    var _getIndexAndLocation5 = getIndexAndLocation(),\n      nextLocation = _getIndexAndLocation5[1]; // Ignore extraneous hashchange events.\n\n    if (createPath(nextLocation) !== createPath(location)) {\n      handlePop();\n    }\n  });\n  var action = Action.Pop;\n  var _getIndexAndLocation6 = getIndexAndLocation(),\n    index = _getIndexAndLocation6[0],\n    location = _getIndexAndLocation6[1];\n  var listeners = createEvents();\n  var blockers = createEvents();\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n  function getBaseHref() {\n    var base = document.querySelector('base');\n    var href = '';\n    if (base && base.getAttribute('href')) {\n      var url = window.location.href;\n      var hashIndex = url.indexOf('#');\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href;\n  }\n  function createHref(to) {\n    return getBaseHref() + '#' + (typeof to === 'string' ? to : createPath(to));\n  }\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n  function applyTx(nextAction) {\n    action = nextAction;\n    var _getIndexAndLocation7 = getIndexAndLocation();\n    index = _getIndexAndLocation7[0];\n    location = _getIndexAndLocation7[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      push(to, state);\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\") : void 0;\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr3 = getHistoryStateAndUrl(nextLocation, index + 1),\n        historyState = _getHistoryStateAndUr3[0],\n        url = _getHistoryStateAndUr3[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n      applyTx(nextAction);\n    }\n  }\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      replace(to, state);\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr4 = getHistoryStateAndUrl(nextLocation, index),\n        historyState = _getHistoryStateAndUr4[0],\n        url = _getHistoryStateAndUr4[1]; // TODO: Support forced reloading\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n  var history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return location;\n    },\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Memory history stores the current location in memory. It is designed for use\r\n * in stateful non-browser environments like tests and React Native.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#creatememoryhistory\r\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options3 = options,\n    _options3$initialEntr = _options3.initialEntries,\n    initialEntries = _options3$initialEntr === void 0 ? ['/'] : _options3$initialEntr,\n    initialIndex = _options3.initialIndex;\n  var entries = initialEntries.map(function (entry) {\n    var location = readOnly(_extends({\n      pathname: '/',\n      search: '',\n      hash: '',\n      state: null,\n      key: createKey()\n    }, typeof entry === 'string' ? parsePath(entry) : entry));\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in createMemoryHistory({ initialEntries }) (invalid entry: \" + JSON.stringify(entry) + \")\") : void 0;\n    return location;\n  });\n  var index = clamp(initialIndex == null ? entries.length - 1 : initialIndex, 0, entries.length - 1);\n  var action = Action.Pop;\n  var location = entries[index];\n  var listeners = createEvents();\n  var blockers = createEvents();\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  }\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n    return readOnly(_extends({\n      pathname: location.pathname,\n      search: '',\n      hash: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n  function applyTx(nextAction, nextLocation) {\n    action = nextAction;\n    location = nextLocation;\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      push(to, state);\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.push(\" + JSON.stringify(to) + \")\") : void 0;\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      applyTx(nextAction, nextLocation);\n    }\n  }\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n    function retry() {\n      replace(to, state);\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n    if (allowTx(nextAction, nextLocation, retry)) {\n      entries[index] = nextLocation;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n  function go(delta) {\n    var nextIndex = clamp(index + delta, 0, entries.length - 1);\n    var nextAction = Action.Pop;\n    var nextLocation = entries[nextIndex];\n    function retry() {\n      go(delta);\n    }\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index = nextIndex;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n  var history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return location;\n    },\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      return blockers.push(blocker);\n    }\n  };\n  return history;\n} ////////////////////////////////////////////////////////////////////////////////\n// UTILS\n////////////////////////////////////////////////////////////////////////////////\n\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\nfunction promptBeforeUnload(event) {\n  // Cancel the event.\n  event.preventDefault(); // Chrome (and legacy IE) requires returnValue to be set.\n\n  event.returnValue = '';\n}\nfunction createEvents() {\n  var handlers = [];\n  return {\n    get length() {\n      return handlers.length;\n    },\n    push: function push(fn) {\n      handlers.push(fn);\n      return function () {\n        handlers = handlers.filter(function (handler) {\n          return handler !== fn;\n        });\n      };\n    },\n    call: function call(arg) {\n      handlers.forEach(function (fn) {\n        return fn && fn(arg);\n      });\n    }\n  };\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\r\n * Creates a string URL path from the given pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createpath\r\n */\n\nfunction createPath(_ref) {\n  var _ref$pathname = _ref.pathname,\n    pathname = _ref$pathname === void 0 ? '/' : _ref$pathname,\n    _ref$search = _ref.search,\n    search = _ref$search === void 0 ? '' : _ref$search,\n    _ref$hash = _ref.hash,\n    hash = _ref$hash === void 0 ? '' : _ref$hash;\n  if (search && search !== '?') pathname += search.charAt(0) === '?' ? search : '?' + search;\n  if (hash && hash !== '#') pathname += hash.charAt(0) === '#' ? hash : '#' + hash;\n  return pathname;\n}\n/**\r\n * Parses a string URL path into its separate pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#parsepath\r\n */\n\nfunction parsePath(path) {\n  var parsedPath = {};\n  if (path) {\n    var hashIndex = path.indexOf('#');\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    var searchIndex = path.indexOf('?');\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nexport { Action, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, parsePath };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\n\n/**\r\n * Actions represent the type of change to a location value.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#action\r\n */\nvar Action;\n\n(function (Action) {\n  /**\r\n   * A POP indicates a change to an arbitrary index in the history stack, such\r\n   * as a back or forward navigation. It does not describe the direction of the\r\n   * navigation, only that the current index changed.\r\n   *\r\n   * Note: This is the default action for newly created history objects.\r\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\r\n   * A PUSH indicates a new entry being added to the history stack, such as when\r\n   * a link is clicked and a new page loads. When this happens, all subsequent\r\n   * entries in the stack are lost.\r\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\r\n   * A REPLACE indicates the entry at the current index in the history stack\r\n   * being replaced by a new one.\r\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\n\nvar readOnly = process.env.NODE_ENV !== \"production\" ? function (obj) {\n  return Object.freeze(obj);\n} : function (obj) {\n  return obj;\n};\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== 'undefined') console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nvar BeforeUnloadEventType = 'beforeunload';\nvar HashChangeEventType = 'hashchange';\nvar PopStateEventType = 'popstate';\n/**\r\n * Browser history stores the location in regular URLs. This is the standard for\r\n * most web apps, but it requires some configuration on the server to ensure you\r\n * serve the same app at multiple URLs.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\r\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$window = _options.window,\n      window = _options$window === void 0 ? document.defaultView : _options$window;\n  var globalHistory = window.history;\n\n  function getIndexAndLocation() {\n    var _window$location = window.location,\n        pathname = _window$location.pathname,\n        search = _window$location.search,\n        hash = _window$location.hash;\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n\n  var blockedPopTx = null;\n\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n\n      var _getIndexAndLocation = getIndexAndLocation(),\n          nextIndex = _getIndexAndLocation[0],\n          nextLocation = _getIndexAndLocation[1];\n\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false, // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better what\n          // is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n\n  window.addEventListener(PopStateEventType, handlePop);\n  var action = Action.Pop;\n\n  var _getIndexAndLocation2 = getIndexAndLocation(),\n      index = _getIndexAndLocation2[0],\n      location = _getIndexAndLocation2[1];\n\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  } // state defaults to `null` because `window.history.state` does\n\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction) {\n    action = nextAction;\n\n    var _getIndexAndLocation3 = getIndexAndLocation();\n\n    index = _getIndexAndLocation3[0];\n    location = _getIndexAndLocation3[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr = getHistoryStateAndUrl(nextLocation, index + 1),\n          historyState = _getHistoryStateAndUr[0],\n          url = _getHistoryStateAndUr[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n\n      applyTx(nextAction);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr2 = getHistoryStateAndUrl(nextLocation, index),\n          historyState = _getHistoryStateAndUr2[0],\n          url = _getHistoryStateAndUr2[1]; // TODO: Support forced reloading\n\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n\n  var history = {\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Hash history stores the location in window.location.hash. This makes it ideal\r\n * for situations where you don't want to send the location to the server for\r\n * some reason, either because you do cannot configure it or the URL space is\r\n * reserved for something else.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\r\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options2 = options,\n      _options2$window = _options2.window,\n      window = _options2$window === void 0 ? document.defaultView : _options2$window;\n  var globalHistory = window.history;\n\n  function getIndexAndLocation() {\n    var _parsePath = parsePath(window.location.hash.substr(1)),\n        _parsePath$pathname = _parsePath.pathname,\n        pathname = _parsePath$pathname === void 0 ? '/' : _parsePath$pathname,\n        _parsePath$search = _parsePath.search,\n        search = _parsePath$search === void 0 ? '' : _parsePath$search,\n        _parsePath$hash = _parsePath.hash,\n        hash = _parsePath$hash === void 0 ? '' : _parsePath$hash;\n\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n\n  var blockedPopTx = null;\n\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n\n      var _getIndexAndLocation4 = getIndexAndLocation(),\n          nextIndex = _getIndexAndLocation4[0],\n          nextLocation = _getIndexAndLocation4[1];\n\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false, // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better\n          // what is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n\n  window.addEventListener(PopStateEventType, handlePop); // popstate does not fire on hashchange in IE 11 and old (trident) Edge\n  // https://developer.mozilla.org/de/docs/Web/API/Window/popstate_event\n\n  window.addEventListener(HashChangeEventType, function () {\n    var _getIndexAndLocation5 = getIndexAndLocation(),\n        nextLocation = _getIndexAndLocation5[1]; // Ignore extraneous hashchange events.\n\n\n    if (createPath(nextLocation) !== createPath(location)) {\n      handlePop();\n    }\n  });\n  var action = Action.Pop;\n\n  var _getIndexAndLocation6 = getIndexAndLocation(),\n      index = _getIndexAndLocation6[0],\n      location = _getIndexAndLocation6[1];\n\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n\n  function getBaseHref() {\n    var base = document.querySelector('base');\n    var href = '';\n\n    if (base && base.getAttribute('href')) {\n      var url = window.location.href;\n      var hashIndex = url.indexOf('#');\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href;\n  }\n\n  function createHref(to) {\n    return getBaseHref() + '#' + (typeof to === 'string' ? to : createPath(to));\n  }\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction) {\n    action = nextAction;\n\n    var _getIndexAndLocation7 = getIndexAndLocation();\n\n    index = _getIndexAndLocation7[0];\n    location = _getIndexAndLocation7[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr3 = getHistoryStateAndUrl(nextLocation, index + 1),\n          historyState = _getHistoryStateAndUr3[0],\n          url = _getHistoryStateAndUr3[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n\n      applyTx(nextAction);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr4 = getHistoryStateAndUrl(nextLocation, index),\n          historyState = _getHistoryStateAndUr4[0],\n          url = _getHistoryStateAndUr4[1]; // TODO: Support forced reloading\n\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n\n  var history = {\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Memory history stores the current location in memory. It is designed for use\r\n * in stateful non-browser environments like tests and React Native.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#creatememoryhistory\r\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options3 = options,\n      _options3$initialEntr = _options3.initialEntries,\n      initialEntries = _options3$initialEntr === void 0 ? ['/'] : _options3$initialEntr,\n      initialIndex = _options3.initialIndex;\n  var entries = initialEntries.map(function (entry) {\n    var location = readOnly(_extends({\n      pathname: '/',\n      search: '',\n      hash: '',\n      state: null,\n      key: createKey()\n    }, typeof entry === 'string' ? parsePath(entry) : entry));\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in createMemoryHistory({ initialEntries }) (invalid entry: \" + JSON.stringify(entry) + \")\") : void 0;\n    return location;\n  });\n  var index = clamp(initialIndex == null ? entries.length - 1 : initialIndex, 0, entries.length - 1);\n  var action = Action.Pop;\n  var location = entries[index];\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  }\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      search: '',\n      hash: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction, nextLocation) {\n    action = nextAction;\n    location = nextLocation;\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.push(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      entries[index] = nextLocation;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  function go(delta) {\n    var nextIndex = clamp(index + delta, 0, entries.length - 1);\n    var nextAction = Action.Pop;\n    var nextLocation = entries[nextIndex];\n\n    function retry() {\n      go(delta);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index = nextIndex;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  var history = {\n    get index() {\n      return index;\n    },\n\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      return blockers.push(blocker);\n    }\n  };\n  return history;\n} ////////////////////////////////////////////////////////////////////////////////\n// UTILS\n////////////////////////////////////////////////////////////////////////////////\n\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\n\nfunction promptBeforeUnload(event) {\n  // Cancel the event.\n  event.preventDefault(); // Chrome (and legacy IE) requires returnValue to be set.\n\n  event.returnValue = '';\n}\n\nfunction createEvents() {\n  var handlers = [];\n  return {\n    get length() {\n      return handlers.length;\n    },\n\n    push: function push(fn) {\n      handlers.push(fn);\n      return function () {\n        handlers = handlers.filter(function (handler) {\n          return handler !== fn;\n        });\n      };\n    },\n    call: function call(arg) {\n      handlers.forEach(function (fn) {\n        return fn && fn(arg);\n      });\n    }\n  };\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\r\n * Creates a string URL path from the given pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createpath\r\n */\n\n\nfunction createPath(_ref) {\n  var _ref$pathname = _ref.pathname,\n      pathname = _ref$pathname === void 0 ? '/' : _ref$pathname,\n      _ref$search = _ref.search,\n      search = _ref$search === void 0 ? '' : _ref$search,\n      _ref$hash = _ref.hash,\n      hash = _ref$hash === void 0 ? '' : _ref$hash;\n  if (search && search !== '?') pathname += search.charAt(0) === '?' ? search : '?' + search;\n  if (hash && hash !== '#') pathname += hash.charAt(0) === '#' ? hash : '#' + hash;\n  return pathname;\n}\n/**\r\n * Parses a string URL path into its separate pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#parsepath\r\n */\n\nfunction parsePath(path) {\n  var parsedPath = {};\n\n  if (path) {\n    var hashIndex = path.indexOf('#');\n\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    var searchIndex = path.indexOf('?');\n\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport { Action, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, parsePath };\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}