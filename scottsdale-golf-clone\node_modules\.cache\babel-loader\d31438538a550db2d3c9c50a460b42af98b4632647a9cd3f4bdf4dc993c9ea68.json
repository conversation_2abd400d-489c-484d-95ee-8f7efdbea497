{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\nimport { productAPI, userAPI } from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  padding: 20px;\n`;\n_c = HomeContainer;\nconst UserInfo = styled.div`\n  background-color: #d3d3d3;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = UserInfo;\nconst UserEmail = styled.div`\n  font-size: 14px;\n  color: #333;\n`;\n_c3 = UserEmail;\nconst VipBadge = styled.span`\n  background-color: #ffd700;\n  color: #333;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: bold;\n`;\n_c4 = VipBadge;\nconst BalanceInfo = styled.div`\n  font-size: 14px;\n  color: #333;\n`;\n_c5 = BalanceInfo;\nconst HeroSection = styled.div`\n  background: linear-gradient(135deg, #4CAF50, #45a049);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: white;\n  background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"80\" r=\"3\" fill=\"rgba(255,255,255,0.1)\"/></svg>');\n`;\n_c6 = HeroSection;\nconst ActionButtons = styled.div`\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n`;\n_c7 = ActionButtons;\nconst ActionButton = styled.button`\n  background: ${props => props.color || '#007bff'};\n  color: white;\n  border: none;\n  padding: 12px 20px;\n  border-radius: 25px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 5px;\n  min-width: 80px;\n`;\n_c8 = ActionButton;\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 600;\n  margin: 20px 0 15px 0;\n  color: #333;\n`;\n_c9 = SectionTitle;\nconst ProductGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n_c0 = ProductGrid;\nconst ProductCard = styled.div`\n  background-color: #e8e8e8;\n  border-radius: 12px;\n  padding: 15px;\n  position: relative;\n`;\n_c1 = ProductCard;\nconst ProductImage = styled.div`\n  width: 100%;\n  height: 120px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n`;\n_c10 = ProductImage;\nconst ProductName = styled.h3`\n  font-size: 14px;\n  font-weight: 500;\n  margin: 5px 0;\n  color: #333;\n`;\n_c11 = ProductName;\nconst ProductPrice = styled.div`\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin: 5px 0;\n`;\n_c12 = ProductPrice;\nconst VipPrice = styled.div`\n  font-size: 12px;\n  color: #666;\n  text-decoration: line-through;\n`;\n_c13 = VipPrice;\nconst VipBadgeProduct = styled.div`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: #ffd700;\n  color: #333;\n  padding: 2px 6px;\n  border-radius: 8px;\n  font-size: 10px;\n  font-weight: bold;\n`;\n_c14 = VipBadgeProduct;\nconst LockIcon = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 24px;\n  color: #666;\n`;\n_c15 = LockIcon;\nconst Home = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [products, setProducts] = useState([]);\n  const [balance, setBalance] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch products and balance in parallel\n        const [productsResponse, balanceResponse] = await Promise.all([productAPI.getProducts({\n          limit: 10\n        }), userAPI.getBalance()]);\n        setProducts(productsResponse.products || []);\n        setBalance(balanceResponse.balance);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching data:', err);\n        setError(err.message || 'Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  const handleProductClick = async product => {\n    if (product.isLocked) {\n      alert(`This product requires VIP${product.vipLevel}. Your current level: VIP${(user === null || user === void 0 ? void 0 : user.vipLevel) || 0}`);\n      return;\n    }\n    if (!product.canPurchase) {\n      alert(product.purchaseReason || 'Cannot purchase this product');\n      return;\n    }\n    if (window.confirm(`Purchase ${product.name} for $${product.price}?`)) {\n      try {\n        await productAPI.purchaseProduct(product._id);\n        alert('Product purchased successfully!');\n\n        // Refresh balance\n        const balanceResponse = await userAPI.getBalance();\n        setBalance(balanceResponse.balance);\n      } catch (err) {\n        alert(err.message || 'Purchase failed');\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading products...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Error loading data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => window.location.reload(),\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(UserInfo, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(UserEmail, {\n          children: \"<EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(VipBadge, {\n          children: \"VIP0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BalanceInfo, {\n        children: \"$0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Task Hall\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n      children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n        color: \"#6c5ce7\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Recharge\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n        color: \"#a29bfe\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDC64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"VIP Line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n        color: \"#fd79a8\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCF1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"App\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n        color: \"#00b894\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Company Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SectionTitle, {\n      children: \"Task Hall\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductGrid, {\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        children: [/*#__PURE__*/_jsxDEV(VipBadgeProduct, {\n          children: product.vipPrice\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ProductImage, {\n          children: [product.icon, product.locked && /*#__PURE__*/_jsxDEV(LockIcon, {\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ProductName, {\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ProductPrice, {\n          children: product.price\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(VipPrice, {\n          children: product.vipPrice\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, product.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"wqDcGFO07Hz69rLlL46C+8fW/fE=\", false, function () {\n  return [useAuth];\n});\n_c16 = Home;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"UserInfo\");\n$RefreshReg$(_c3, \"UserEmail\");\n$RefreshReg$(_c4, \"VipBadge\");\n$RefreshReg$(_c5, \"BalanceInfo\");\n$RefreshReg$(_c6, \"HeroSection\");\n$RefreshReg$(_c7, \"ActionButtons\");\n$RefreshReg$(_c8, \"ActionButton\");\n$RefreshReg$(_c9, \"SectionTitle\");\n$RefreshReg$(_c0, \"ProductGrid\");\n$RefreshReg$(_c1, \"ProductCard\");\n$RefreshReg$(_c10, \"ProductImage\");\n$RefreshReg$(_c11, \"ProductName\");\n$RefreshReg$(_c12, \"ProductPrice\");\n$RefreshReg$(_c13, \"VipPrice\");\n$RefreshReg$(_c14, \"VipBadgeProduct\");\n$RefreshReg$(_c15, \"LockIcon\");\n$RefreshReg$(_c16, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useAuth", "productAPI", "userAPI", "LoadingSpinner", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "UserInfo", "_c2", "UserEmail", "_c3", "VipBadge", "span", "_c4", "BalanceInfo", "_c5", "HeroSection", "_c6", "ActionButtons", "_c7", "ActionButton", "button", "props", "color", "_c8", "SectionTitle", "h2", "_c9", "ProductGrid", "_c0", "ProductCard", "_c1", "ProductImage", "_c10", "ProductName", "h3", "_c11", "ProductPrice", "_c12", "VipPrice", "_c13", "VipBadgeProduct", "_c14", "LockIcon", "_c15", "Home", "_s", "user", "products", "setProducts", "balance", "setBalance", "loading", "setLoading", "error", "setError", "fetchData", "productsResponse", "balanceResponse", "Promise", "all", "getProducts", "limit", "getBalance", "err", "console", "message", "handleProductClick", "product", "isLocked", "alert", "vipLevel", "canPurchase", "purchaseReason", "window", "confirm", "name", "price", "purchaseProduct", "_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "textAlign", "children", "onClick", "location", "reload", "map", "vipPrice", "icon", "locked", "id", "_c16", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\nimport { productAPI, userAPI } from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst HomeContainer = styled.div`\n  padding: 20px;\n`;\n\nconst UserInfo = styled.div`\n  background-color: #d3d3d3;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst UserEmail = styled.div`\n  font-size: 14px;\n  color: #333;\n`;\n\nconst VipBadge = styled.span`\n  background-color: #ffd700;\n  color: #333;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: bold;\n`;\n\nconst BalanceInfo = styled.div`\n  font-size: 14px;\n  color: #333;\n`;\n\nconst HeroSection = styled.div`\n  background: linear-gradient(135deg, #4CAF50, #45a049);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: white;\n  background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"80\" r=\"3\" fill=\"rgba(255,255,255,0.1)\"/></svg>');\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n`;\n\nconst ActionButton = styled.button`\n  background: ${props => props.color || '#007bff'};\n  color: white;\n  border: none;\n  padding: 12px 20px;\n  border-radius: 25px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 5px;\n  min-width: 80px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 18px;\n  font-weight: 600;\n  margin: 20px 0 15px 0;\n  color: #333;\n`;\n\nconst ProductGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n\nconst ProductCard = styled.div`\n  background-color: #e8e8e8;\n  border-radius: 12px;\n  padding: 15px;\n  position: relative;\n`;\n\nconst ProductImage = styled.div`\n  width: 100%;\n  height: 120px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48px;\n`;\n\nconst ProductName = styled.h3`\n  font-size: 14px;\n  font-weight: 500;\n  margin: 5px 0;\n  color: #333;\n`;\n\nconst ProductPrice = styled.div`\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin: 5px 0;\n`;\n\nconst VipPrice = styled.div`\n  font-size: 12px;\n  color: #666;\n  text-decoration: line-through;\n`;\n\nconst VipBadgeProduct = styled.div`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: #ffd700;\n  color: #333;\n  padding: 2px 6px;\n  border-radius: 8px;\n  font-size: 10px;\n  font-weight: bold;\n`;\n\nconst LockIcon = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 24px;\n  color: #666;\n`;\n\nconst Home = () => {\n  const { user } = useAuth();\n  const [products, setProducts] = useState([]);\n  const [balance, setBalance] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch products and balance in parallel\n        const [productsResponse, balanceResponse] = await Promise.all([\n          productAPI.getProducts({ limit: 10 }),\n          userAPI.getBalance()\n        ]);\n\n        setProducts(productsResponse.products || []);\n        setBalance(balanceResponse.balance);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching data:', err);\n        setError(err.message || 'Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const handleProductClick = async (product) => {\n    if (product.isLocked) {\n      alert(`This product requires VIP${product.vipLevel}. Your current level: VIP${user?.vipLevel || 0}`);\n      return;\n    }\n\n    if (!product.canPurchase) {\n      alert(product.purchaseReason || 'Cannot purchase this product');\n      return;\n    }\n\n    if (window.confirm(`Purchase ${product.name} for $${product.price}?`)) {\n      try {\n        await productAPI.purchaseProduct(product._id);\n        alert('Product purchased successfully!');\n\n        // Refresh balance\n        const balanceResponse = await userAPI.getBalance();\n        setBalance(balanceResponse.balance);\n      } catch (err) {\n        alert(err.message || 'Purchase failed');\n      }\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner message=\"Loading products...\" />;\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: '20px', textAlign: 'center' }}>\n        <h3>Error loading data</h3>\n        <p>{error}</p>\n        <button onClick={() => window.location.reload()}>Retry</button>\n      </div>\n    );\n  }\n\n  return (\n    <HomeContainer>\n      <UserInfo>\n        <div>\n          <UserEmail><EMAIL></UserEmail>\n          <VipBadge>VIP0</VipBadge>\n        </div>\n        <BalanceInfo>$0</BalanceInfo>\n      </UserInfo>\n\n      <HeroSection>\n        <h2>Task Hall</h2>\n      </HeroSection>\n\n      <ActionButtons>\n        <ActionButton color=\"#6c5ce7\">\n          <span>💰</span>\n          <span>Recharge</span>\n        </ActionButton>\n        <ActionButton color=\"#a29bfe\">\n          <span>👤</span>\n          <span>VIP Line</span>\n        </ActionButton>\n        <ActionButton color=\"#fd79a8\">\n          <span>📱</span>\n          <span>App</span>\n        </ActionButton>\n        <ActionButton color=\"#00b894\">\n          <span>👥</span>\n          <span>Company Profile</span>\n        </ActionButton>\n      </ActionButtons>\n\n      <SectionTitle>Task Hall</SectionTitle>\n      <ProductGrid>\n        {products.map((product) => (\n          <ProductCard key={product.id}>\n            <VipBadgeProduct>{product.vipPrice}</VipBadgeProduct>\n            <ProductImage>\n              {product.icon}\n              {product.locked && <LockIcon>🔒</LockIcon>}\n            </ProductImage>\n            <ProductName>{product.name}</ProductName>\n            <ProductPrice>{product.price}</ProductPrice>\n            <VipPrice>{product.vipPrice}</VipPrice>\n          </ProductCard>\n        ))}\n      </ProductGrid>\n    </HomeContainer>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,UAAU,EAAEC,OAAO,QAAQ,iBAAiB;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,aAAa,GAAGP,MAAM,CAACQ,GAAG;AAChC;AACA,CAAC;AAACC,EAAA,GAFIF,aAAa;AAInB,MAAMG,QAAQ,GAAGV,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,QAAQ;AAUd,MAAME,SAAS,GAAGZ,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA,CAAC;AAACK,GAAA,GAHID,SAAS;AAKf,MAAME,QAAQ,GAAGd,MAAM,CAACe,IAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,QAAQ;AASd,MAAMG,WAAW,GAAGjB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA,CAAC;AAACU,GAAA,GAHID,WAAW;AAKjB,MAAME,WAAW,GAAGnB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAPID,WAAW;AASjB,MAAME,aAAa,GAAGrB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGvB,MAAM,CAACwB,MAAM;AAClC,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAI,SAAS;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIJ,YAAY;AAgBlB,MAAMK,YAAY,GAAG5B,MAAM,CAAC6B,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAG/B,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAGjC,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GALID,WAAW;AAOjB,MAAME,YAAY,GAAGnC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAVID,YAAY;AAYlB,MAAME,WAAW,GAAGrC,MAAM,CAACsC,EAAE;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,WAAW;AAOjB,MAAMG,YAAY,GAAGxC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GALID,YAAY;AAOlB,MAAME,QAAQ,GAAG1C,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAJID,QAAQ;AAMd,MAAME,eAAe,GAAG5C,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAVID,eAAe;AAYrB,MAAME,QAAQ,GAAG9C,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAPID,QAAQ;AASd,MAAME,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC;EAAK,CAAC,GAAGjD,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAM4D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAM,CAACI,gBAAgB,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5D7D,UAAU,CAAC8D,WAAW,CAAC;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC,EACrC9D,OAAO,CAAC+D,UAAU,CAAC,CAAC,CACrB,CAAC;QAEFd,WAAW,CAACQ,gBAAgB,CAACT,QAAQ,IAAI,EAAE,CAAC;QAC5CG,UAAU,CAACO,eAAe,CAACR,OAAO,CAAC;QACnCK,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZC,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEU,GAAG,CAAC;QAC1CT,QAAQ,CAACS,GAAG,CAACE,OAAO,IAAI,qBAAqB,CAAC;MAChD,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,kBAAkB,GAAG,MAAOC,OAAO,IAAK;IAC5C,IAAIA,OAAO,CAACC,QAAQ,EAAE;MACpBC,KAAK,CAAC,4BAA4BF,OAAO,CAACG,QAAQ,4BAA4B,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,QAAQ,KAAI,CAAC,EAAE,CAAC;MACpG;IACF;IAEA,IAAI,CAACH,OAAO,CAACI,WAAW,EAAE;MACxBF,KAAK,CAACF,OAAO,CAACK,cAAc,IAAI,8BAA8B,CAAC;MAC/D;IACF;IAEA,IAAIC,MAAM,CAACC,OAAO,CAAC,YAAYP,OAAO,CAACQ,IAAI,SAASR,OAAO,CAACS,KAAK,GAAG,CAAC,EAAE;MACrE,IAAI;QACF,MAAM9E,UAAU,CAAC+E,eAAe,CAACV,OAAO,CAACW,GAAG,CAAC;QAC7CT,KAAK,CAAC,iCAAiC,CAAC;;QAExC;QACA,MAAMZ,eAAe,GAAG,MAAM1D,OAAO,CAAC+D,UAAU,CAAC,CAAC;QAClDZ,UAAU,CAACO,eAAe,CAACR,OAAO,CAAC;MACrC,CAAC,CAAC,OAAOc,GAAG,EAAE;QACZM,KAAK,CAACN,GAAG,CAACE,OAAO,IAAI,iBAAiB,CAAC;MACzC;IACF;EACF,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBAAOjD,OAAA,CAACF,cAAc;MAACiE,OAAO,EAAC;IAAqB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzD;EAEA,IAAI7B,KAAK,EAAE;IACT,oBACEnD,OAAA;MAAKiF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnDpF,OAAA;QAAAoF,QAAA,EAAI;MAAkB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BhF,OAAA;QAAAoF,QAAA,EAAIjC;MAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdhF,OAAA;QAAQqF,OAAO,EAAEA,CAAA,KAAMd,MAAM,CAACe,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAH,QAAA,EAAC;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEV;EAEA,oBACEhF,OAAA,CAACC,aAAa;IAAAmF,QAAA,gBACZpF,OAAA,CAACI,QAAQ;MAAAgF,QAAA,gBACPpF,OAAA;QAAAoF,QAAA,gBACEpF,OAAA,CAACM,SAAS;UAAA8E,QAAA,EAAC;QAAiB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACxChF,OAAA,CAACQ,QAAQ;UAAA4E,QAAA,EAAC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACNhF,OAAA,CAACW,WAAW;QAAAyE,QAAA,EAAC;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEXhF,OAAA,CAACa,WAAW;MAAAuE,QAAA,eACVpF,OAAA;QAAAoF,QAAA,EAAI;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEdhF,OAAA,CAACe,aAAa;MAAAqE,QAAA,gBACZpF,OAAA,CAACiB,YAAY;QAACG,KAAK,EAAC,SAAS;QAAAgE,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfhF,OAAA;UAAAoF,QAAA,EAAM;QAAQ;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACfhF,OAAA,CAACiB,YAAY;QAACG,KAAK,EAAC,SAAS;QAAAgE,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfhF,OAAA;UAAAoF,QAAA,EAAM;QAAQ;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACfhF,OAAA,CAACiB,YAAY;QAACG,KAAK,EAAC,SAAS;QAAAgE,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfhF,OAAA;UAAAoF,QAAA,EAAM;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACfhF,OAAA,CAACiB,YAAY;QAACG,KAAK,EAAC,SAAS;QAAAgE,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAM;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfhF,OAAA;UAAAoF,QAAA,EAAM;QAAe;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEhBhF,OAAA,CAACsB,YAAY;MAAA8D,QAAA,EAAC;IAAS;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eACtChF,OAAA,CAACyB,WAAW;MAAA2D,QAAA,EACTvC,QAAQ,CAAC2C,GAAG,CAAEvB,OAAO,iBACpBjE,OAAA,CAAC2B,WAAW;QAAAyD,QAAA,gBACVpF,OAAA,CAACsC,eAAe;UAAA8C,QAAA,EAAEnB,OAAO,CAACwB;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eACrDhF,OAAA,CAAC6B,YAAY;UAAAuD,QAAA,GACVnB,OAAO,CAACyB,IAAI,EACZzB,OAAO,CAAC0B,MAAM,iBAAI3F,OAAA,CAACwC,QAAQ;YAAA4C,QAAA,EAAC;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACfhF,OAAA,CAAC+B,WAAW;UAAAqD,QAAA,EAAEnB,OAAO,CAACQ;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACzChF,OAAA,CAACkC,YAAY;UAAAkD,QAAA,EAAEnB,OAAO,CAACS;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAC5ChF,OAAA,CAACoC,QAAQ;UAAAgD,QAAA,EAAEnB,OAAO,CAACwB;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA,GARvBf,OAAO,CAAC2B,EAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASf,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAACrC,EAAA,CAzHID,IAAI;EAAA,QACS/C,OAAO;AAAA;AAAAkG,IAAA,GADpBnD,IAAI;AA2HV,eAAeA,IAAI;AAAC,IAAAvC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAoD,IAAA;AAAAC,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}