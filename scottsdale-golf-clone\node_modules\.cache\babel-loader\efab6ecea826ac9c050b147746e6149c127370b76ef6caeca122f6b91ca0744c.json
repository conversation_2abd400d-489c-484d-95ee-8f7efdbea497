{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\App.js\";\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Context and Services\nimport { AuthProvider, useAuth } from './services/AuthContext';\nimport socketService from './services/socket';\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n_c = AppContainer;\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n_c2 = MainContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/task\",\n            element: /*#__PURE__*/_jsxDEV(Task, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/team\",\n            element: /*#__PURE__*/_jsxDEV(Team, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/vip\",\n            element: /*#__PURE__*/_jsxDEV(VIP, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BottomNavigation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "styled", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "socketService", "Header", "BottomNavigation", "LoadingSpinner", "Home", "Task", "Team", "VIP", "Profile", "Register", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "MainContent", "main", "_c2", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Context and Services\nimport { AuthProvider, useAuth } from './services/AuthContext';\nimport socketService from './services/socket';\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n\nfunction App() {\n  return (\n    <Router>\n      <AppContainer>\n        <Header />\n        <MainContent>\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/task\" element={<Task />} />\n            <Route path=\"/team\" element={<Team />} />\n            <Route path=\"/vip\" element={<VIP />} />\n            <Route path=\"/profile\" element={<Profile />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/login\" element={<Login />} />\n          </Routes>\n        </MainContent>\n        <BottomNavigation />\n      </AppContainer>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,WAAW;;AAElB;AACA,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;;AAE7C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGhB,MAAM,CAACiB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAGnB,MAAM,CAACoB,IAAI;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,WAAW;AAKjB,SAASG,GAAGA,CAAA,EAAG;EACb,oBACEP,OAAA,CAACnB,MAAM;IAAA2B,QAAA,eACLR,OAAA,CAACC,YAAY;MAAAO,QAAA,gBACXR,OAAA,CAACX,MAAM;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVZ,OAAA,CAACI,WAAW;QAAAI,QAAA,eACVR,OAAA,CAAClB,MAAM;UAAA0B,QAAA,gBACLR,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEd,OAAA,CAACR,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEd,OAAA,CAACP,IAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEd,OAAA,CAACN,IAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,MAAM;YAACC,OAAO,eAAEd,OAAA,CAACL,GAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEd,OAAA,CAACJ,OAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEd,OAAA,CAACH,QAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDZ,OAAA,CAACjB,KAAK;YAAC8B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEd,OAAA,CAACF,KAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdZ,OAAA,CAACV,gBAAgB;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb;AAACG,GAAA,GApBQR,GAAG;AAsBZ,eAAeA,GAAG;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}