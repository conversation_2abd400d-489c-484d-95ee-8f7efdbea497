{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance\nconst API = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\nAPI.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\nAPI.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response, _error$response2;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n});\n\n// Auth API\nexport const authAPI = {\n  register: userData => API.post('/auth/register', userData),\n  login: credentials => API.post('/auth/login', credentials),\n  getMe: () => API.get('/auth/me'),\n  updateProfile: profileData => API.put('/auth/profile', profileData),\n  changePassword: passwordData => API.put('/auth/password', passwordData),\n  logout: () => API.post('/auth/logout')\n};\n\n// User API\nexport const userAPI = {\n  getBalance: () => API.get('/users/balance'),\n  getReferralInfo: () => API.get('/users/referral'),\n  getTeamStats: () => API.get('/users/team'),\n  getTransactions: params => API.get('/users/transactions', {\n    params\n  }),\n  getUserStats: () => API.get('/users/stats'),\n  getVipMembers: () => API.get('/users/vip-members'),\n  updateVipLevel: () => API.put('/users/vip-level')\n};\n\n// Product API\nexport const productAPI = {\n  getProducts: params => API.get('/products', {\n    params\n  }),\n  getProduct: id => API.get(`/products/${id}`),\n  purchaseProduct: id => API.post(`/products/${id}/purchase`),\n  addReview: (id, reviewData) => API.post(`/products/${id}/review`, reviewData),\n  getCategories: () => API.get('/products/categories'),\n  getFeaturedProducts: () => API.get('/products/featured')\n};\n\n// Task API\nexport const taskAPI = {\n  getTasks: params => API.get('/tasks', {\n    params\n  }),\n  getDailyTasks: () => API.get('/tasks/daily'),\n  getTaskStats: () => API.get('/tasks/stats'),\n  startTask: id => API.post(`/tasks/${id}/start`),\n  completeTask: id => API.post(`/tasks/${id}/complete`)\n};\n\n// Team API\nexport const teamAPI = {\n  getTeamOverview: () => API.get('/teams/overview'),\n  getReferralStats: () => API.get('/teams/referral-stats'),\n  getTeamPerformance: params => API.get('/teams/performance', {\n    params\n  }),\n  getTeamMember: id => API.get(`/teams/member/${id}`)\n};\n\n// Transaction API\nexport const transactionAPI = {\n  createRecharge: rechargeData => API.post('/transactions/recharge', rechargeData),\n  createWithdrawal: withdrawalData => API.post('/transactions/withdraw', withdrawalData),\n  getTransactionHistory: params => API.get('/transactions/history', {\n    params\n  }),\n  getTransaction: id => API.get(`/transactions/${id}`),\n  cancelTransaction: (id, reason) => API.put(`/transactions/${id}/cancel`, {\n    reason\n  }),\n  getFinancialSummary: params => API.get('/transactions/summary', {\n    params\n  })\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => API.get('/health')\n};\nexport default API;", "map": {"version": 3, "names": ["axios", "API", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "_error$response2", "status", "removeItem", "window", "location", "href", "message", "authAPI", "register", "userData", "post", "login", "credentials", "getMe", "get", "updateProfile", "profileData", "put", "changePassword", "passwordData", "logout", "userAPI", "getBalance", "getReferralInfo", "getTeamStats", "getTransactions", "params", "getUserStats", "getVipMembers", "updateVipLevel", "productAPI", "getProducts", "getProduct", "id", "purchaseProduct", "add<PERSON>eview", "reviewData", "getCategories", "getFeaturedProducts", "taskAPI", "getTasks", "getDailyTasks", "getTaskStats", "startTask", "completeTask", "teamAPI", "getTeamOverview", "getReferralStats", "getTeamPerformance", "getTeamMember", "transactionAPI", "createRecharge", "rechargeData", "createWithdrawal", "withdrawalData", "getTransactionHistory", "getTransaction", "cancelTransaction", "reason", "getFinancialSummary", "healthAPI", "check"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance\nconst API = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\nAPI.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\nAPI.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error.response?.data || error.message);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: (userData) => API.post('/auth/register', userData),\n  login: (credentials) => API.post('/auth/login', credentials),\n  getMe: () => API.get('/auth/me'),\n  updateProfile: (profileData) => API.put('/auth/profile', profileData),\n  changePassword: (passwordData) => API.put('/auth/password', passwordData),\n  logout: () => API.post('/auth/logout'),\n};\n\n// User API\nexport const userAPI = {\n  getBalance: () => API.get('/users/balance'),\n  getReferralInfo: () => API.get('/users/referral'),\n  getTeamStats: () => API.get('/users/team'),\n  getTransactions: (params) => API.get('/users/transactions', { params }),\n  getUserStats: () => API.get('/users/stats'),\n  getVipMembers: () => API.get('/users/vip-members'),\n  updateVipLevel: () => API.put('/users/vip-level'),\n};\n\n// Product API\nexport const productAPI = {\n  getProducts: (params) => API.get('/products', { params }),\n  getProduct: (id) => API.get(`/products/${id}`),\n  purchaseProduct: (id) => API.post(`/products/${id}/purchase`),\n  addReview: (id, reviewData) => API.post(`/products/${id}/review`, reviewData),\n  getCategories: () => API.get('/products/categories'),\n  getFeaturedProducts: () => API.get('/products/featured'),\n};\n\n// Task API\nexport const taskAPI = {\n  getTasks: (params) => API.get('/tasks', { params }),\n  getDailyTasks: () => API.get('/tasks/daily'),\n  getTaskStats: () => API.get('/tasks/stats'),\n  startTask: (id) => API.post(`/tasks/${id}/start`),\n  completeTask: (id) => API.post(`/tasks/${id}/complete`),\n};\n\n// Team API\nexport const teamAPI = {\n  getTeamOverview: () => API.get('/teams/overview'),\n  getReferralStats: () => API.get('/teams/referral-stats'),\n  getTeamPerformance: (params) => API.get('/teams/performance', { params }),\n  getTeamMember: (id) => API.get(`/teams/member/${id}`),\n};\n\n// Transaction API\nexport const transactionAPI = {\n  createRecharge: (rechargeData) => API.post('/transactions/recharge', rechargeData),\n  createWithdrawal: (withdrawalData) => API.post('/transactions/withdraw', withdrawalData),\n  getTransactionHistory: (params) => API.get('/transactions/history', { params }),\n  getTransaction: (id) => API.get(`/transactions/${id}`),\n  cancelTransaction: (id, reason) => API.put(`/transactions/${id}/cancel`, { reason }),\n  getFinancialSummary: (params) => API.get('/transactions/summary', { params }),\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => API.get('/health'),\n};\n\nexport default API;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,gBAAA;EACT,IAAI,EAAAD,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;IAClC;IACAV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;IAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOV,OAAO,CAACC,MAAM,CAAC,EAAAI,gBAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBF,IAAI,KAAIJ,KAAK,CAACY,OAAO,CAAC;AAC9D,CACF,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAK/B,GAAG,CAACgC,IAAI,CAAC,gBAAgB,EAAED,QAAQ,CAAC;EAC5DE,KAAK,EAAGC,WAAW,IAAKlC,GAAG,CAACgC,IAAI,CAAC,aAAa,EAAEE,WAAW,CAAC;EAC5DC,KAAK,EAAEA,CAAA,KAAMnC,GAAG,CAACoC,GAAG,CAAC,UAAU,CAAC;EAChCC,aAAa,EAAGC,WAAW,IAAKtC,GAAG,CAACuC,GAAG,CAAC,eAAe,EAAED,WAAW,CAAC;EACrEE,cAAc,EAAGC,YAAY,IAAKzC,GAAG,CAACuC,GAAG,CAAC,gBAAgB,EAAEE,YAAY,CAAC;EACzEC,MAAM,EAAEA,CAAA,KAAM1C,GAAG,CAACgC,IAAI,CAAC,cAAc;AACvC,CAAC;;AAED;AACA,OAAO,MAAMW,OAAO,GAAG;EACrBC,UAAU,EAAEA,CAAA,KAAM5C,GAAG,CAACoC,GAAG,CAAC,gBAAgB,CAAC;EAC3CS,eAAe,EAAEA,CAAA,KAAM7C,GAAG,CAACoC,GAAG,CAAC,iBAAiB,CAAC;EACjDU,YAAY,EAAEA,CAAA,KAAM9C,GAAG,CAACoC,GAAG,CAAC,aAAa,CAAC;EAC1CW,eAAe,EAAGC,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,qBAAqB,EAAE;IAAEY;EAAO,CAAC,CAAC;EACvEC,YAAY,EAAEA,CAAA,KAAMjD,GAAG,CAACoC,GAAG,CAAC,cAAc,CAAC;EAC3Cc,aAAa,EAAEA,CAAA,KAAMlD,GAAG,CAACoC,GAAG,CAAC,oBAAoB,CAAC;EAClDe,cAAc,EAAEA,CAAA,KAAMnD,GAAG,CAACuC,GAAG,CAAC,kBAAkB;AAClD,CAAC;;AAED;AACA,OAAO,MAAMa,UAAU,GAAG;EACxBC,WAAW,EAAGL,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,WAAW,EAAE;IAAEY;EAAO,CAAC,CAAC;EACzDM,UAAU,EAAGC,EAAE,IAAKvD,GAAG,CAACoC,GAAG,CAAC,aAAamB,EAAE,EAAE,CAAC;EAC9CC,eAAe,EAAGD,EAAE,IAAKvD,GAAG,CAACgC,IAAI,CAAC,aAAauB,EAAE,WAAW,CAAC;EAC7DE,SAAS,EAAEA,CAACF,EAAE,EAAEG,UAAU,KAAK1D,GAAG,CAACgC,IAAI,CAAC,aAAauB,EAAE,SAAS,EAAEG,UAAU,CAAC;EAC7EC,aAAa,EAAEA,CAAA,KAAM3D,GAAG,CAACoC,GAAG,CAAC,sBAAsB,CAAC;EACpDwB,mBAAmB,EAAEA,CAAA,KAAM5D,GAAG,CAACoC,GAAG,CAAC,oBAAoB;AACzD,CAAC;;AAED;AACA,OAAO,MAAMyB,OAAO,GAAG;EACrBC,QAAQ,EAAGd,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,QAAQ,EAAE;IAAEY;EAAO,CAAC,CAAC;EACnDe,aAAa,EAAEA,CAAA,KAAM/D,GAAG,CAACoC,GAAG,CAAC,cAAc,CAAC;EAC5C4B,YAAY,EAAEA,CAAA,KAAMhE,GAAG,CAACoC,GAAG,CAAC,cAAc,CAAC;EAC3C6B,SAAS,EAAGV,EAAE,IAAKvD,GAAG,CAACgC,IAAI,CAAC,UAAUuB,EAAE,QAAQ,CAAC;EACjDW,YAAY,EAAGX,EAAE,IAAKvD,GAAG,CAACgC,IAAI,CAAC,UAAUuB,EAAE,WAAW;AACxD,CAAC;;AAED;AACA,OAAO,MAAMY,OAAO,GAAG;EACrBC,eAAe,EAAEA,CAAA,KAAMpE,GAAG,CAACoC,GAAG,CAAC,iBAAiB,CAAC;EACjDiC,gBAAgB,EAAEA,CAAA,KAAMrE,GAAG,CAACoC,GAAG,CAAC,uBAAuB,CAAC;EACxDkC,kBAAkB,EAAGtB,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,oBAAoB,EAAE;IAAEY;EAAO,CAAC,CAAC;EACzEuB,aAAa,EAAGhB,EAAE,IAAKvD,GAAG,CAACoC,GAAG,CAAC,iBAAiBmB,EAAE,EAAE;AACtD,CAAC;;AAED;AACA,OAAO,MAAMiB,cAAc,GAAG;EAC5BC,cAAc,EAAGC,YAAY,IAAK1E,GAAG,CAACgC,IAAI,CAAC,wBAAwB,EAAE0C,YAAY,CAAC;EAClFC,gBAAgB,EAAGC,cAAc,IAAK5E,GAAG,CAACgC,IAAI,CAAC,wBAAwB,EAAE4C,cAAc,CAAC;EACxFC,qBAAqB,EAAG7B,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,uBAAuB,EAAE;IAAEY;EAAO,CAAC,CAAC;EAC/E8B,cAAc,EAAGvB,EAAE,IAAKvD,GAAG,CAACoC,GAAG,CAAC,iBAAiBmB,EAAE,EAAE,CAAC;EACtDwB,iBAAiB,EAAEA,CAACxB,EAAE,EAAEyB,MAAM,KAAKhF,GAAG,CAACuC,GAAG,CAAC,iBAAiBgB,EAAE,SAAS,EAAE;IAAEyB;EAAO,CAAC,CAAC;EACpFC,mBAAmB,EAAGjC,MAAM,IAAKhD,GAAG,CAACoC,GAAG,CAAC,uBAAuB,EAAE;IAAEY;EAAO,CAAC;AAC9E,CAAC;;AAED;AACA,OAAO,MAAMkC,SAAS,GAAG;EACvBC,KAAK,EAAEA,CAAA,KAAMnF,GAAG,CAACoC,GAAG,CAAC,SAAS;AAChC,CAAC;AAED,eAAepC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}