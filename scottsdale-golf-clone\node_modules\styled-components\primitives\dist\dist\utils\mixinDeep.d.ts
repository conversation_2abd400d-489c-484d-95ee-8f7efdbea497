import { ExtensibleObject } from '../types';
/**
 * Arrays & POJOs merged recursively, other objects and value types are overridden
 * If target is not a POJO or an Array, it will get source properties injected via shallow merge
 * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.
 */
export default function mixinDeep(target?: ExtensibleObject, ...sources: any[]): ExtensibleObject;
//# sourceMappingURL=mixinDeep.d.ts.map