{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\pages\\\\Team.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamContainer = styled.div`\n  padding: 20px;\n`;\n_c = TeamContainer;\nconst InvitationCard = styled.div`\n  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n_c2 = InvitationCard;\nconst InvitationTitle = styled.div`\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n`;\n_c3 = InvitationTitle;\nconst InvitationCode = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;\n_c4 = InvitationCode;\nconst InvitationLink = styled.div`\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 15px;\n  word-break: break-all;\n`;\n_c5 = InvitationLink;\nconst CopyButton = styled.button`\n  background-color: #333;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  margin-left: 10px;\n`;\n_c6 = CopyButton;\nconst SocialIcons = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-top: 15px;\n`;\n_c7 = SocialIcons;\nconst SocialIcon = styled.div`\n  font-size: 20px;\n  cursor: pointer;\n`;\n_c8 = SocialIcon;\nconst StatsCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n_c9 = StatsCard;\nconst StatsRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  text-align: center;\n`;\n_c0 = StatsRow;\nconst StatItem = styled.div`\n  flex: 1;\n`;\n_c1 = StatItem;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 5px;\n`;\n_c10 = StatLabel;\nconst StatValue = styled.div`\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n`;\n_c11 = StatValue;\nconst LevelCards = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n_c12 = LevelCards;\nconst LevelCard = styled.div`\n  flex: 1;\n  background: ${props => props.gradient};\n  border-radius: 12px;\n  padding: 20px;\n  color: white;\n  text-align: center;\n`;\n_c13 = LevelCard;\nconst LevelTitle = styled.div`\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n_c14 = LevelTitle;\nconst LevelStats = styled.div`\n  font-size: 12px;\n  margin-bottom: 5px;\n`;\n_c15 = LevelStats;\nconst LevelValue = styled.div`\n  font-size: 18px;\n  font-weight: bold;\n`;\n_c16 = LevelValue;\nconst DetailsButton = styled.button`\n  background-color: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  margin-top: 10px;\n  width: 100%;\n`;\n_c17 = DetailsButton;\nconst Team = () => {\n  return /*#__PURE__*/_jsxDEV(TeamContainer, {\n    children: [/*#__PURE__*/_jsxDEV(InvitationCard, {\n      children: [/*#__PURE__*/_jsxDEV(InvitationTitle, {\n        children: \"Invitation code:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(InvitationCode, {\n          children: \"607651\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CopyButton, {\n          children: \"Copy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InvitationTitle, {\n        children: \"Share your referral link and start earning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(InvitationLink, {\n          children: \"https://scottsdalegolf.cc/#/register?ref=607651\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CopyButton, {\n          children: \"Copy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SocialIcons, {\n        children: [/*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\u274C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCD8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCE7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCBC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCF7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83C\\uDFB5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SocialIcon, {\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: '10px'\n          },\n          children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Selection period\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsRow, {\n        children: [/*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Team size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Team recharge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"$0.00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Team Withdrawal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"$0.00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsRow, {\n        style: {\n          marginTop: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"New team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"First time recharge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"First withdrawal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LevelCards, {\n      children: [/*#__PURE__*/_jsxDEV(LevelCard, {\n        gradient: \"linear-gradient(135deg, #00cec9, #55a3ff)\",\n        children: [/*#__PURE__*/_jsxDEV(LevelTitle, {\n          children: \"\\uD83C\\uDFAF LEV 1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Register/Valid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0/0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Total Income\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailsButton, {\n          children: \"Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LevelCard, {\n        gradient: \"linear-gradient(135deg, #a29bfe, #6c5ce7)\",\n        children: [/*#__PURE__*/_jsxDEV(LevelTitle, {\n          children: \"\\uD83C\\uDFAF LEV 2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Register/Valid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0/0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Total Income\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailsButton, {\n          children: \"Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LevelCard, {\n        gradient: \"linear-gradient(135deg, #fd79a8, #e84393)\",\n        children: [/*#__PURE__*/_jsxDEV(LevelTitle, {\n          children: \"\\uD83C\\uDFC6 LEV 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Register/Valid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0/0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelStats, {\n          children: \"Total Income\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LevelValue, {\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailsButton, {\n          children: \"Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_c18 = Team;\nexport default Team;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"TeamContainer\");\n$RefreshReg$(_c2, \"InvitationCard\");\n$RefreshReg$(_c3, \"InvitationTitle\");\n$RefreshReg$(_c4, \"InvitationCode\");\n$RefreshReg$(_c5, \"InvitationLink\");\n$RefreshReg$(_c6, \"CopyButton\");\n$RefreshReg$(_c7, \"SocialIcons\");\n$RefreshReg$(_c8, \"SocialIcon\");\n$RefreshReg$(_c9, \"StatsCard\");\n$RefreshReg$(_c0, \"StatsRow\");\n$RefreshReg$(_c1, \"StatItem\");\n$RefreshReg$(_c10, \"StatLabel\");\n$RefreshReg$(_c11, \"StatValue\");\n$RefreshReg$(_c12, \"LevelCards\");\n$RefreshReg$(_c13, \"LevelCard\");\n$RefreshReg$(_c14, \"LevelTitle\");\n$RefreshReg$(_c15, \"LevelStats\");\n$RefreshReg$(_c16, \"LevelValue\");\n$RefreshReg$(_c17, \"DetailsButton\");\n$RefreshReg$(_c18, \"Team\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "TeamContainer", "div", "_c", "InvitationCard", "_c2", "InvitationTitle", "_c3", "InvitationCode", "_c4", "InvitationLink", "_c5", "Copy<PERSON><PERSON><PERSON>", "button", "_c6", "SocialIcons", "_c7", "SocialIcon", "_c8", "StatsCard", "_c9", "StatsRow", "_c0", "StatItem", "_c1", "StatLabel", "_c10", "StatValue", "_c11", "LevelCards", "_c12", "LevelCard", "props", "gradient", "_c13", "LevelTitle", "_c14", "LevelStats", "_c15", "LevelValue", "_c16", "DetailsButton", "_c17", "Team", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "marginBottom", "marginRight", "marginTop", "_c18", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/pages/Team.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst TeamContainer = styled.div`\n  padding: 20px;\n`;\n\nconst InvitationCard = styled.div`\n  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n\nconst InvitationTitle = styled.div`\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n`;\n\nconst InvitationCode = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst InvitationLink = styled.div`\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 15px;\n  word-break: break-all;\n`;\n\nconst CopyButton = styled.button`\n  background-color: #333;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  margin-left: 10px;\n`;\n\nconst SocialIcons = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-top: 15px;\n`;\n\nconst SocialIcon = styled.div`\n  font-size: 20px;\n  cursor: pointer;\n`;\n\nconst StatsCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatsRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  text-align: center;\n`;\n\nconst StatItem = styled.div`\n  flex: 1;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 5px;\n`;\n\nconst StatValue = styled.div`\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n`;\n\nconst LevelCards = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n\nconst LevelCard = styled.div`\n  flex: 1;\n  background: ${props => props.gradient};\n  border-radius: 12px;\n  padding: 20px;\n  color: white;\n  text-align: center;\n`;\n\nconst LevelTitle = styled.div`\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n\nconst LevelStats = styled.div`\n  font-size: 12px;\n  margin-bottom: 5px;\n`;\n\nconst LevelValue = styled.div`\n  font-size: 18px;\n  font-weight: bold;\n`;\n\nconst DetailsButton = styled.button`\n  background-color: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  margin-top: 10px;\n  width: 100%;\n`;\n\nconst Team = () => {\n  return (\n    <TeamContainer>\n      <InvitationCard>\n        <InvitationTitle>Invitation code:</InvitationTitle>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <InvitationCode>607651</InvitationCode>\n          <CopyButton>Copy</CopyButton>\n        </div>\n        <InvitationTitle>Share your referral link and start earning</InvitationTitle>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <InvitationLink>https://scottsdalegolf.cc/#/register?ref=607651</InvitationLink>\n          <CopyButton>Copy</CopyButton>\n        </div>\n        <SocialIcons>\n          <SocialIcon>❌</SocialIcon>\n          <SocialIcon>📘</SocialIcon>\n          <SocialIcon>📧</SocialIcon>\n          <SocialIcon>💼</SocialIcon>\n          <SocialIcon>💬</SocialIcon>\n          <SocialIcon>📷</SocialIcon>\n          <SocialIcon>🎵</SocialIcon>\n          <SocialIcon>📋</SocialIcon>\n        </SocialIcons>\n      </InvitationCard>\n\n      <StatsCard>\n        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>\n          <span style={{ marginRight: '10px' }}>🇺🇸</span>\n          <span>Selection period</span>\n        </div>\n        <StatsRow>\n          <StatItem>\n            <StatLabel>Team size</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n          <StatItem>\n            <StatLabel>Team recharge</StatLabel>\n            <StatValue>$0.00</StatValue>\n          </StatItem>\n          <StatItem>\n            <StatLabel>Team Withdrawal</StatLabel>\n            <StatValue>$0.00</StatValue>\n          </StatItem>\n        </StatsRow>\n        <StatsRow style={{ marginTop: '15px' }}>\n          <StatItem>\n            <StatLabel>New team</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n          <StatItem>\n            <StatLabel>First time recharge</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n          <StatItem>\n            <StatLabel>First withdrawal</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n        </StatsRow>\n      </StatsCard>\n\n      <LevelCards>\n        <LevelCard gradient=\"linear-gradient(135deg, #00cec9, #55a3ff)\">\n          <LevelTitle>🎯 LEV 1</LevelTitle>\n          <LevelStats>Register/Valid</LevelStats>\n          <LevelValue>0/0</LevelValue>\n          <LevelStats>Total Income</LevelStats>\n          <LevelValue>0</LevelValue>\n          <DetailsButton>Details</DetailsButton>\n        </LevelCard>\n        \n        <LevelCard gradient=\"linear-gradient(135deg, #a29bfe, #6c5ce7)\">\n          <LevelTitle>🎯 LEV 2</LevelTitle>\n          <LevelStats>Register/Valid</LevelStats>\n          <LevelValue>0/0</LevelValue>\n          <LevelStats>Total Income</LevelStats>\n          <LevelValue>0</LevelValue>\n          <DetailsButton>Details</DetailsButton>\n        </LevelCard>\n        \n        <LevelCard gradient=\"linear-gradient(135deg, #fd79a8, #e84393)\">\n          <LevelTitle>🏆 LEV 3</LevelTitle>\n          <LevelStats>Register/Valid</LevelStats>\n          <LevelValue>0/0</LevelValue>\n          <LevelStats>Total Income</LevelStats>\n          <LevelValue>0</LevelValue>\n          <DetailsButton>Details</DetailsButton>\n        </LevelCard>\n      </LevelCards>\n    </TeamContainer>\n  );\n};\n\nexport default Team;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA,CAAC;AAACC,EAAA,GAFIF,aAAa;AAInB,MAAMG,cAAc,GAAGN,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,cAAc;AAOpB,MAAME,eAAe,GAAGR,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,eAAe;AAMrB,MAAME,cAAc,GAAGV,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAGZ,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GALID,cAAc;AAOpB,MAAME,UAAU,GAAGd,MAAM,CAACe,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,UAAU;AAWhB,MAAMG,WAAW,GAAGjB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,WAAW;AAMjB,MAAME,UAAU,GAAGnB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA,CAAC;AAACgB,GAAA,GAHID,UAAU;AAKhB,MAAME,SAAS,GAAGrB,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGvB,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGzB,MAAM,CAACI,GAAG;AAC3B;AACA,CAAC;AAACsB,GAAA,GAFID,QAAQ;AAId,MAAME,SAAS,GAAG3B,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACwB,IAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAG7B,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAJID,SAAS;AAMf,MAAME,UAAU,GAAG/B,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAJID,UAAU;AAMhB,MAAME,SAAS,GAAGjC,MAAM,CAACI,GAAG;AAC5B;AACA,gBAAgB8B,KAAK,IAAIA,KAAK,CAACC,QAAQ;AACvC;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIH,SAAS;AASf,MAAMI,UAAU,GAAGrC,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGvC,MAAM,CAACI,GAAG;AAC7B;AACA;AACA,CAAC;AAACoC,IAAA,GAHID,UAAU;AAKhB,MAAME,UAAU,GAAGzC,MAAM,CAACI,GAAG;AAC7B;AACA;AACA,CAAC;AAACsC,IAAA,GAHID,UAAU;AAKhB,MAAME,aAAa,GAAG3C,MAAM,CAACe,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAVID,aAAa;AAYnB,MAAME,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACE3C,OAAA,CAACC,aAAa;IAAA2C,QAAA,gBACZ5C,OAAA,CAACI,cAAc;MAAAwC,QAAA,gBACb5C,OAAA,CAACM,eAAe;QAAAsC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eACnDhD,OAAA;QAAKiD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAP,QAAA,gBACpD5C,OAAA,CAACQ,cAAc;UAAAoC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACvChD,OAAA,CAACY,UAAU;UAAAgC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACNhD,OAAA,CAACM,eAAe;QAAAsC,QAAA,EAAC;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAC7EhD,OAAA;QAAKiD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAP,QAAA,gBACpD5C,OAAA,CAACU,cAAc;UAAAkC,QAAA,EAAC;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAChFhD,OAAA,CAACY,UAAU;UAAAgC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACNhD,OAAA,CAACe,WAAW;QAAA6B,QAAA,gBACV5C,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhD,OAAA,CAACiB,UAAU;UAAA2B,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEjBhD,OAAA,CAACmB,SAAS;MAAAyB,QAAA,gBACR5C,OAAA;QAAKiD,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,gBAC1E5C,OAAA;UAAMiD,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjDhD,OAAA;UAAA4C,QAAA,EAAM;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACNhD,OAAA,CAACqB,QAAQ;QAAAuB,QAAA,gBACP5C,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChChD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACXhD,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACpChD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACXhD,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACtChD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACXhD,OAAA,CAACqB,QAAQ;QAAC4B,KAAK,EAAE;UAAEK,SAAS,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACrC5C,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC/BhD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACXhD,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1ChD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACXhD,OAAA,CAACuB,QAAQ;UAAAqB,QAAA,gBACP5C,OAAA,CAACyB,SAAS;YAAAmB,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACvChD,OAAA,CAAC2B,SAAS;YAAAiB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEZhD,OAAA,CAAC6B,UAAU;MAAAe,QAAA,gBACT5C,OAAA,CAAC+B,SAAS;QAACE,QAAQ,EAAC,2CAA2C;QAAAW,QAAA,gBAC7D5C,OAAA,CAACmC,UAAU;UAAAS,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACjChD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5BhD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1BhD,OAAA,CAACyC,aAAa;UAAAG,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEZhD,OAAA,CAAC+B,SAAS;QAACE,QAAQ,EAAC,2CAA2C;QAAAW,QAAA,gBAC7D5C,OAAA,CAACmC,UAAU;UAAAS,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACjChD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5BhD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1BhD,OAAA,CAACyC,aAAa;UAAAG,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEZhD,OAAA,CAAC+B,SAAS;QAACE,QAAQ,EAAC,2CAA2C;QAAAW,QAAA,gBAC7D5C,OAAA,CAACmC,UAAU;UAAAS,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACjChD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5BhD,OAAA,CAACqC,UAAU;UAAAO,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrChD,OAAA,CAACuC,UAAU;UAAAK,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1BhD,OAAA,CAACyC,aAAa;UAAAG,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEpB,CAAC;AAACO,IAAA,GA3FIZ,IAAI;AA6FV,eAAeA,IAAI;AAAC,IAAAxC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAa,IAAA;AAAAC,YAAA,CAAArD,EAAA;AAAAqD,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA1B,IAAA;AAAA0B,YAAA,CAAAtB,IAAA;AAAAsB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAd,IAAA;AAAAc,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}