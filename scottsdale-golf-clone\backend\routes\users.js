const express = require('express');
const {
  getBalance,
  getReferralInfo,
  getTeamStats,
  getTransactions,
  updateVipLevel,
  getUserStats,
  getVipMembers
} = require('../controllers/userController');
const { protect, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting
const userRateLimit = rateLimit(15 * 60 * 1000, 50); // 50 requests per 15 minutes

// All routes are protected
router.use(protect);
router.use(userRateLimit);

// User routes
router.get('/balance', getBalance);
router.get('/referral', getReferralInfo);
router.get('/team', getTeamStats);
router.get('/transactions', getTransactions);
router.get('/stats', getUserStats);
router.get('/vip-members', getVipMembers);
router.put('/vip-level', updateVipLevel);

module.exports = router;
