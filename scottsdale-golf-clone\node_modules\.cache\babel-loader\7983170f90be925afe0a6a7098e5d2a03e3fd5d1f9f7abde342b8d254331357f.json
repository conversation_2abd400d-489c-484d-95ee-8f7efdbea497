{"ast": null, "code": "import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n    bytesNotified = loaded;\n    const data = {\n      loaded,\n      total,\n      progress: total ? loaded / total : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n    listener(data);\n  }, freq);\n};\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n  return [loaded => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n};\nexport const asyncDecorator = fn => (...args) => utils.asap(() => fn(...args));", "map": {"version": 3, "names": ["speedometer", "throttle", "utils", "progressEventReducer", "listener", "isDownloadStream", "freq", "bytesNotified", "_speedometer", "e", "loaded", "total", "lengthComputable", "undefined", "progressBytes", "rate", "inRange", "data", "progress", "bytes", "estimated", "event", "progressEventDecorator", "throttled", "asyncDecorator", "fn", "args", "asap"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/node_modules/axios/lib/helpers/progressEventReducer.js"], "sourcesContent": ["import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\n\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n\n    listener(data);\n  }, freq);\n}\n\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n\n  return [(loaded) => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n}\n\nexport const asyncDecorator = (fn) => (...args) => utils.asap(() => fn(...args));\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,aAAa;AAE/B,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,QAAQ,EAAEC,gBAAgB,EAAEC,IAAI,GAAG,CAAC,KAAK;EAC5E,IAAIC,aAAa,GAAG,CAAC;EACrB,MAAMC,YAAY,GAAGR,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC;EAEzC,OAAOC,QAAQ,CAACQ,CAAC,IAAI;IACnB,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAAM;IACvB,MAAMC,KAAK,GAAGF,CAAC,CAACG,gBAAgB,GAAGH,CAAC,CAACE,KAAK,GAAGE,SAAS;IACtD,MAAMC,aAAa,GAAGJ,MAAM,GAAGH,aAAa;IAC5C,MAAMQ,IAAI,GAAGP,YAAY,CAACM,aAAa,CAAC;IACxC,MAAME,OAAO,GAAGN,MAAM,IAAIC,KAAK;IAE/BJ,aAAa,GAAGG,MAAM;IAEtB,MAAMO,IAAI,GAAG;MACXP,MAAM;MACNC,KAAK;MACLO,QAAQ,EAAEP,KAAK,GAAID,MAAM,GAAGC,KAAK,GAAIE,SAAS;MAC9CM,KAAK,EAAEL,aAAa;MACpBC,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAGF,SAAS;MAC7BO,SAAS,EAAEL,IAAI,IAAIJ,KAAK,IAAIK,OAAO,GAAG,CAACL,KAAK,GAAGD,MAAM,IAAIK,IAAI,GAAGF,SAAS;MACzEQ,KAAK,EAAEZ,CAAC;MACRG,gBAAgB,EAAED,KAAK,IAAI,IAAI;MAC/B,CAACN,gBAAgB,GAAG,UAAU,GAAG,QAAQ,GAAG;IAC9C,CAAC;IAEDD,QAAQ,CAACa,IAAI,CAAC;EAChB,CAAC,EAAEX,IAAI,CAAC;AACV,CAAC;AAED,OAAO,MAAMgB,sBAAsB,GAAGA,CAACX,KAAK,EAAEY,SAAS,KAAK;EAC1D,MAAMX,gBAAgB,GAAGD,KAAK,IAAI,IAAI;EAEtC,OAAO,CAAED,MAAM,IAAKa,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/BX,gBAAgB;IAChBD,KAAK;IACLD;EACF,CAAC,CAAC,EAAEa,SAAS,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIC,EAAE,IAAK,CAAC,GAAGC,IAAI,KAAKxB,KAAK,CAACyB,IAAI,CAAC,MAAMF,EAAE,CAAC,GAAGC,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}