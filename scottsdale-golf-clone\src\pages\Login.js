import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

const LoginContainer = styled.div`
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 40px;
`;

const LogoIcon = styled.div`
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
`;

const LogoText = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #333;
`;

const WelcomeText = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const WelcomeTitle = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
`;

const WelcomeSubtitle = styled.p`
  font-size: 16px;
  color: #666;
  margin: 0;
`;

const FormCard = styled.div`
  background-color: white;
  border-radius: 16px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #ff6b35;
  }

  &::placeholder {
    color: #999;
  }
`;

const PasswordContainer = styled.div`
  position: relative;
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #666;
`;

const SubmitButton = styled.button`
  width: 100%;
  background-color: #8b5a3c;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #7a4d33;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const RegisterLink = styled.div`
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
`;

const RegisterButton = styled.button`
  background: none;
  border: none;
  color: #ff6b35;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
`;

const ForgotPassword = styled.div`
  text-align: right;
  margin-top: 10px;
`;

const ForgotPasswordLink = styled.button`
  background: none;
  border: none;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
`;

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle login logic here
    console.log('Login data:', formData);
    navigate('/');
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <LoginContainer>
      <Logo>
        <LogoIcon>S</LogoIcon>
        <LogoText>scottsdalegolf Mall</LogoText>
      </Logo>

      <WelcomeText>
        <WelcomeTitle>Welcome back</WelcomeTitle>
        <WelcomeSubtitle>Sign in to continue</WelcomeSubtitle>
      </WelcomeText>

      <FormCard>
        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>Email</Label>
            <Input
              type="email"
              name="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Password</Label>
            <PasswordContainer>
              <Input
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleInputChange}
                required
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '🙈' : '👁️'}
              </PasswordToggle>
            </PasswordContainer>
            <ForgotPassword>
              <ForgotPasswordLink type="button">
                Forgot password?
              </ForgotPasswordLink>
            </ForgotPassword>
          </FormGroup>

          <SubmitButton type="submit">
            Sign In
          </SubmitButton>
        </form>

        <RegisterLink>
          Don't have an account?{' '}
          <RegisterButton onClick={() => navigate('/register')}>
            Sign Up
          </RegisterButton>
        </RegisterLink>
      </FormCard>
    </LoginContainer>
  );
};

export default Login;
