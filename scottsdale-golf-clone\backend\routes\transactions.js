const express = require('express');
const {
  createRecharge,
  createWithdrawal,
  getTransactionHistory,
  getTransaction,
  cancelTransaction,
  getFinancialSummary
} = require('../controllers/transactionController');
const { protect, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting
const transactionRateLimit = rateLimit(15 * 60 * 1000, 30); // 30 requests per 15 minutes

// All routes are protected
router.use(protect);
router.use(transactionRateLimit);

// Transaction routes
router.post('/recharge', createRecharge);
router.post('/withdraw', createWithdrawal);
router.get('/history', getTransactionHistory);
router.get('/summary', getFinancialSummary);
router.get('/:id', getTransaction);
router.put('/:id/cancel', cancelTransaction);

module.exports = router;
