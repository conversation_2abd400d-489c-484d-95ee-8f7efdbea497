{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Test if basic React is working\nfunction TestComponent() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"React App is Working!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"If you can see this, React is running correctly.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n\n// Components\n_c = TestComponent;\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\nfunction App() {\n  // Temporarily return test component to debug\n  return /*#__PURE__*/_jsxDEV(TestComponent, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 10\n  }, this);\n\n  /*return (\n    <Router>\n      <AppContainer>\n        <Header />\n        <MainContent>\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/task\" element={<Task />} />\n            <Route path=\"/team\" element={<Team />} />\n            <Route path=\"/vip\" element={<VIP />} />\n            <Route path=\"/profile\" element={<Profile />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/login\" element={<Login />} />\n          </Routes>\n        </MainContent>\n        <BottomNavigation />\n      </AppContainer>\n    </Router>\n  );*/\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"TestComponent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "styled", "TestComponent", "_jsxDEV", "style", "padding", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Header", "BottomNavigation", "Home", "Task", "Team", "VIP", "Profile", "Register", "<PERSON><PERSON>", "jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "MainContent", "main", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Test if basic React is working\nfunction TestComponent() {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1>React App is Working!</h1>\n      <p>If you can see this, React is running correctly.</p>\n    </div>\n  );\n}\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n\nfunction App() {\n  // Temporarily return test component to debug\n  return <TestComponent />;\n\n  /*return (\n    <Router>\n      <AppContainer>\n        <Header />\n        <MainContent>\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/task\" element={<Task />} />\n            <Route path=\"/team\" element={<Team />} />\n            <Route path=\"/vip\" element={<VIP />} />\n            <Route path=\"/profile\" element={<Profile />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/login\" element={<Login />} />\n          </Routes>\n        </MainContent>\n        <BottomNavigation />\n      </AppContainer>\n    </Router>\n  );*/\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,WAAW;;AAElB;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,oBACEC,OAAA;IAAKC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDJ,OAAA;MAAAI,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BR,OAAA;MAAAI,QAAA,EAAG;IAAgD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;;AAEA;AAAAC,EAAA,GATSV,aAAa;AAUtB,OAAOW,MAAM,MAAM,qBAAqB;AACxC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAnB,OAAA;AAElC,MAAMoB,YAAY,GAAGtB,MAAM,CAACuB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGxB,MAAM,CAACyB,IAAI;AAC/B;AACA;AACA,CAAC;AAED,SAASC,GAAGA,CAAA,EAAG;EACb;EACA,oBAAOxB,OAAA,CAACD,aAAa;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;;EAExB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAACiB,GAAA,GAvBQD,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAf,EAAA,EAAAgB,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}