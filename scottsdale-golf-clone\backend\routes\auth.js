const express = require('express');
const {
  register,
  login,
  getMe,
  updateProfile,
  changePassword,
  logout
} = require('../controllers/authController');
const { protect, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting to auth routes
const authRateLimit = rateLimit(15 * 60 * 1000, 5); // 5 requests per 15 minutes

// Public routes
router.post('/register', authRateLimit, register);
router.post('/login', authRateLimit, login);

// Protected routes
router.get('/me', protect, getMe);
router.put('/profile', protect, updateProfile);
router.put('/password', protect, changePassword);
router.post('/logout', protect, logout);

module.exports = router;
