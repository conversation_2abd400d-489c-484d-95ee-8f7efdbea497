const User = require('../models/User');
const Transaction = require('../models/Transaction');

// @desc    Get team overview
// @route   GET /api/teams/overview
// @access  Private
const getTeamOverview = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get direct referrals (Level 1)
    const level1Members = await User.find({
      'referral.referredBy': req.user.id
    }).select('email profile vipLevel balance createdAt');
    
    // Get Level 2 members (referrals of Level 1 members)
    const level2Members = await User.find({
      'referral.referredBy': { $in: level1Members.map(member => member._id) }
    }).select('email profile vipLevel balance createdAt');
    
    // Get Level 3 members (referrals of Level 2 members)
    const level3Members = await User.find({
      'referral.referredBy': { $in: level2Members.map(member => member._id) }
    }).select('email profile vipLevel balance createdAt');
    
    // Calculate statistics
    const stats = {
      level1: {
        count: level1Members.length,
        totalBalance: level1Members.reduce((sum, member) => sum + member.balance.total, 0),
        totalEarnings: 0 // Will be calculated from transactions
      },
      level2: {
        count: level2Members.length,
        totalBalance: level2Members.reduce((sum, member) => sum + member.balance.total, 0),
        totalEarnings: 0
      },
      level3: {
        count: level3Members.length,
        totalBalance: level3Members.reduce((sum, member) => sum + member.balance.total, 0),
        totalEarnings: 0
      }
    };
    
    // Get referral earnings
    const referralEarnings = await Transaction.find({
      user: req.user.id,
      type: 'referral'
    });
    
    const totalReferralEarnings = referralEarnings.reduce((sum, transaction) => sum + transaction.amount, 0);
    
    res.status(200).json({
      success: true,
      team: {
        overview: user.team,
        referral: {
          code: user.referral.code,
          link: `${process.env.CLIENT_URL}/#/register?ref=${user.referral.code}`,
          totalEarnings: totalReferralEarnings
        },
        levels: stats,
        members: {
          level1: level1Members.map(member => ({
            id: member._id,
            email: member.email.replace(/(.{1})(.*)(@.*)/, '$1***$3'),
            vipLevel: member.vipLevel,
            balance: member.balance.total,
            joinedAt: member.createdAt
          })),
          level2: level2Members.map(member => ({
            id: member._id,
            email: member.email.replace(/(.{1})(.*)(@.*)/, '$1***$3'),
            vipLevel: member.vipLevel,
            balance: member.balance.total,
            joinedAt: member.createdAt
          })),
          level3: level3Members.map(member => ({
            id: member._id,
            email: member.email.replace(/(.{1})(.*)(@.*)/, '$1***$3'),
            vipLevel: member.vipLevel,
            balance: member.balance.total,
            joinedAt: member.createdAt
          }))
        }
      }
    });
  } catch (error) {
    console.error('Get team overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting team overview'
    });
  }
};

// @desc    Get referral statistics
// @route   GET /api/teams/referral-stats
// @access  Private
const getReferralStats = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get referral transactions
    const referralTransactions = await Transaction.find({
      user: req.user.id,
      type: 'referral'
    }).sort({ createdAt: -1 });
    
    // Get monthly referral stats
    const monthlyStats = await Transaction.aggregate([
      {
        $match: {
          user: user._id,
          type: 'referral',
          createdAt: { $gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) } // Last year
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          totalEarnings: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': -1, '_id.month': -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      referralStats: {
        code: user.referral.code,
        totalReferrals: user.referral.totalReferrals,
        totalEarnings: user.referral.totalEarnings,
        recentTransactions: referralTransactions.slice(0, 10),
        monthlyStats
      }
    });
  } catch (error) {
    console.error('Get referral stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting referral statistics'
    });
  }
};

// @desc    Get team member details
// @route   GET /api/teams/member/:id
// @access  Private
const getTeamMember = async (req, res) => {
  try {
    const member = await User.findById(req.params.id)
      .select('email profile vipLevel balance team referral createdAt lastLogin');
    
    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Team member not found'
      });
    }
    
    // Check if this member is in user's team
    const isInTeam = await User.findOne({
      _id: req.user.id,
      'referral.referredUsers.user': member._id
    });
    
    if (!isInTeam) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this member'
      });
    }
    
    // Get member's recent activity
    const recentTransactions = await Transaction.find({
      user: member._id
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .select('type amount description createdAt');
    
    res.status(200).json({
      success: true,
      member: {
        id: member._id,
        email: member.email.replace(/(.{1})(.*)(@.*)/, '$1***$3'),
        profile: member.profile,
        vipLevel: member.vipLevel,
        balance: member.balance,
        team: member.team,
        referral: {
          totalReferrals: member.referral.totalReferrals,
          totalEarnings: member.referral.totalEarnings
        },
        joinedAt: member.createdAt,
        lastLogin: member.lastLogin,
        recentActivity: recentTransactions
      }
    });
  } catch (error) {
    console.error('Get team member error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting team member details'
    });
  }
};

// @desc    Get team performance
// @route   GET /api/teams/performance
// @access  Private
const getTeamPerformance = async (req, res) => {
  try {
    const period = req.query.period || 'month'; // day, week, month, year
    
    let dateFilter;
    const now = new Date();
    
    switch (period) {
      case 'day':
        dateFilter = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        dateFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        dateFilter = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        dateFilter = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        dateFilter = new Date(now.getFullYear(), now.getMonth(), 1);
    }
    
    // Get team members
    const teamMembers = await User.find({
      'referral.referredBy': req.user.id
    }).select('_id');
    
    const memberIds = teamMembers.map(member => member._id);
    
    // Get team performance data
    const performance = await Transaction.aggregate([
      {
        $match: {
          user: { $in: memberIds },
          createdAt: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: '$type',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Get new members in period
    const newMembers = await User.countDocuments({
      'referral.referredBy': req.user.id,
      createdAt: { $gte: dateFilter }
    });
    
    res.status(200).json({
      success: true,
      performance: {
        period,
        newMembers,
        transactions: performance,
        totalTeamSize: memberIds.length
      }
    });
  } catch (error) {
    console.error('Get team performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting team performance'
    });
  }
};

module.exports = {
  getTeamOverview,
  getReferralStats,
  getTeamMember,
  getTeamPerformance
};
