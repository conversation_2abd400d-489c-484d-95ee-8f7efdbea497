{"ast": null, "code": "import { io } from 'socket.io-client';\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n  }\n  connect(userId) {\n    if (this.socket) {\n      this.disconnect();\n    }\n    this.socket = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n      transports: ['websocket'],\n      autoConnect: true\n    });\n    this.socket.on('connect', () => {\n      console.log('Connected to server');\n      this.isConnected = true;\n\n      // Join user-specific room\n      if (userId) {\n        this.socket.emit('join-user-room', userId);\n      }\n    });\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n      this.isConnected = false;\n    });\n    this.socket.on('connect_error', error => {\n      console.error('Connection error:', error);\n      this.isConnected = false;\n    });\n    return this.socket;\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  // Listen for real-time events\n  onNewReferral(callback) {\n    if (this.socket) {\n      this.socket.on('new-referral', callback);\n    }\n  }\n  onTaskStarted(callback) {\n    if (this.socket) {\n      this.socket.on('task-started', callback);\n    }\n  }\n  onTaskCompleted(callback) {\n    if (this.socket) {\n      this.socket.on('task-completed', callback);\n    }\n  }\n  onPurchaseSuccess(callback) {\n    if (this.socket) {\n      this.socket.on('purchase-success', callback);\n    }\n  }\n  onRechargeCreated(callback) {\n    if (this.socket) {\n      this.socket.on('recharge-created', callback);\n    }\n  }\n  onWithdrawalCreated(callback) {\n    if (this.socket) {\n      this.socket.on('withdrawal-created', callback);\n    }\n  }\n  onTransactionCancelled(callback) {\n    if (this.socket) {\n      this.socket.on('transaction-cancelled', callback);\n    }\n  }\n\n  // Remove event listeners\n  offNewReferral() {\n    if (this.socket) {\n      this.socket.off('new-referral');\n    }\n  }\n  offTaskStarted() {\n    if (this.socket) {\n      this.socket.off('task-started');\n    }\n  }\n  offTaskCompleted() {\n    if (this.socket) {\n      this.socket.off('task-completed');\n    }\n  }\n  offPurchaseSuccess() {\n    if (this.socket) {\n      this.socket.off('purchase-success');\n    }\n  }\n  offRechargeCreated() {\n    if (this.socket) {\n      this.socket.off('recharge-created');\n    }\n  }\n  offWithdrawalCreated() {\n    if (this.socket) {\n      this.socket.off('withdrawal-created');\n    }\n  }\n  offTransactionCancelled() {\n    if (this.socket) {\n      this.socket.off('transaction-cancelled');\n    }\n  }\n\n  // Generic emit function\n  emit(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Generic listener function\n  on(event, callback) {\n    if (this.socket) {\n      this.socket.on(event, callback);\n    }\n  }\n\n  // Generic remove listener function\n  off(event) {\n    if (this.socket) {\n      this.socket.off(event);\n    }\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\nexport default socketService;", "map": {"version": 3, "names": ["io", "SocketService", "constructor", "socket", "isConnected", "connect", "userId", "disconnect", "process", "env", "REACT_APP_API_URL", "transports", "autoConnect", "on", "console", "log", "emit", "error", "onNewReferral", "callback", "onTaskStarted", "onTaskCompleted", "onPurchaseSuccess", "on<PERSON><PERSON><PERSON>geCreated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d", "onTransactionCancelled", "offNewReferral", "off", "offTaskStarted", "offTaskCompleted", "offPurchaseSuccess", "offRechargeCreated", "<PERSON><PERSON>ith<PERSON><PERSON><PERSON>reated", "offTransactionCancelled", "event", "data", "socketService"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/services/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n  }\n\n  connect(userId) {\n    if (this.socket) {\n      this.disconnect();\n    }\n\n    this.socket = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n      transports: ['websocket'],\n      autoConnect: true,\n    });\n\n    this.socket.on('connect', () => {\n      console.log('Connected to server');\n      this.isConnected = true;\n      \n      // Join user-specific room\n      if (userId) {\n        this.socket.emit('join-user-room', userId);\n      }\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n      this.isConnected = false;\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Connection error:', error);\n      this.isConnected = false;\n    });\n\n    return this.socket;\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n    }\n  }\n\n  // Listen for real-time events\n  onNewReferral(callback) {\n    if (this.socket) {\n      this.socket.on('new-referral', callback);\n    }\n  }\n\n  onTaskStarted(callback) {\n    if (this.socket) {\n      this.socket.on('task-started', callback);\n    }\n  }\n\n  onTaskCompleted(callback) {\n    if (this.socket) {\n      this.socket.on('task-completed', callback);\n    }\n  }\n\n  onPurchaseSuccess(callback) {\n    if (this.socket) {\n      this.socket.on('purchase-success', callback);\n    }\n  }\n\n  onRechargeCreated(callback) {\n    if (this.socket) {\n      this.socket.on('recharge-created', callback);\n    }\n  }\n\n  onWithdrawalCreated(callback) {\n    if (this.socket) {\n      this.socket.on('withdrawal-created', callback);\n    }\n  }\n\n  onTransactionCancelled(callback) {\n    if (this.socket) {\n      this.socket.on('transaction-cancelled', callback);\n    }\n  }\n\n  // Remove event listeners\n  offNewReferral() {\n    if (this.socket) {\n      this.socket.off('new-referral');\n    }\n  }\n\n  offTaskStarted() {\n    if (this.socket) {\n      this.socket.off('task-started');\n    }\n  }\n\n  offTaskCompleted() {\n    if (this.socket) {\n      this.socket.off('task-completed');\n    }\n  }\n\n  offPurchaseSuccess() {\n    if (this.socket) {\n      this.socket.off('purchase-success');\n    }\n  }\n\n  offRechargeCreated() {\n    if (this.socket) {\n      this.socket.off('recharge-created');\n    }\n  }\n\n  offWithdrawalCreated() {\n    if (this.socket) {\n      this.socket.off('withdrawal-created');\n    }\n  }\n\n  offTransactionCancelled() {\n    if (this.socket) {\n      this.socket.off('transaction-cancelled');\n    }\n  }\n\n  // Generic emit function\n  emit(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Generic listener function\n  on(event, callback) {\n    if (this.socket) {\n      this.socket.on(event, callback);\n    }\n  }\n\n  // Generic remove listener function\n  off(event) {\n    if (this.socket) {\n      this.socket.off(event);\n    }\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,kBAAkB;AAErC,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC1B;EAEAC,OAAOA,CAACC,MAAM,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACI,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI,CAACJ,MAAM,GAAGH,EAAE,CAACQ,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;MACzEC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,IAAI,CAACT,MAAM,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;MAC9BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,IAAI,CAACX,WAAW,GAAG,IAAI;;MAEvB;MACA,IAAIE,MAAM,EAAE;QACV,IAAI,CAACH,MAAM,CAACa,IAAI,CAAC,gBAAgB,EAAEV,MAAM,CAAC;MAC5C;IACF,CAAC,CAAC;IAEF,IAAI,CAACH,MAAM,CAACU,EAAE,CAAC,YAAY,EAAE,MAAM;MACjCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAI,CAACX,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACD,MAAM,CAACU,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;MACzCH,OAAO,CAACG,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,IAAI,CAACb,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;IAEF,OAAO,IAAI,CAACD,MAAM;EACpB;EAEAI,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,UAAU,CAAC,CAAC;MACxB,IAAI,CAACJ,MAAM,GAAG,IAAI;MAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IAC1B;EACF;;EAEA;EACAc,aAAaA,CAACC,QAAQ,EAAE;IACtB,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,cAAc,EAAEM,QAAQ,CAAC;IAC1C;EACF;EAEAC,aAAaA,CAACD,QAAQ,EAAE;IACtB,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,cAAc,EAAEM,QAAQ,CAAC;IAC1C;EACF;EAEAE,eAAeA,CAACF,QAAQ,EAAE;IACxB,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,gBAAgB,EAAEM,QAAQ,CAAC;IAC5C;EACF;EAEAG,iBAAiBA,CAACH,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;IAC9C;EACF;EAEAI,iBAAiBA,CAACJ,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;IAC9C;EACF;EAEAK,mBAAmBA,CAACL,QAAQ,EAAE;IAC5B,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;IAChD;EACF;EAEAM,sBAAsBA,CAACN,QAAQ,EAAE;IAC/B,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,uBAAuB,EAAEM,QAAQ,CAAC;IACnD;EACF;;EAEA;EACAO,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACvB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;IACjC;EACF;EAEAC,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACzB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,cAAc,CAAC;IACjC;EACF;EAEAE,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,gBAAgB,CAAC;IACnC;EACF;EAEAG,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC3B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,kBAAkB,CAAC;IACrC;EACF;EAEAI,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,kBAAkB,CAAC;IACrC;EACF;EAEAK,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC7B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,oBAAoB,CAAC;IACvC;EACF;EAEAM,uBAAuBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAAC9B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAAC,uBAAuB,CAAC;IAC1C;EACF;;EAEA;EACAX,IAAIA,CAACkB,KAAK,EAAEC,IAAI,EAAE;IAChB,IAAI,IAAI,CAAChC,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACa,IAAI,CAACkB,KAAK,EAAEC,IAAI,CAAC;IAC/B;EACF;;EAEA;EACAtB,EAAEA,CAACqB,KAAK,EAAEf,QAAQ,EAAE;IAClB,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACU,EAAE,CAACqB,KAAK,EAAEf,QAAQ,CAAC;IACjC;EACF;;EAEA;EACAQ,GAAGA,CAACO,KAAK,EAAE;IACT,IAAI,IAAI,CAAC/B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAACO,KAAK,CAAC;IACxB;EACF;AACF;;AAEA;AACA,MAAME,aAAa,GAAG,IAAInC,aAAa,CAAC,CAAC;AAEzC,eAAemC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}