{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegisterContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n`;\n_c = RegisterContainer;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 40px;\n`;\n_c2 = Logo;\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 24px;\n`;\n_c3 = LogoIcon;\nconst LogoText = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n`;\n_c4 = LogoText;\nconst WelcomeText = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n_c5 = WelcomeText;\nconst WelcomeTitle = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 0 5px 0;\n`;\n_c6 = WelcomeTitle;\nconst WelcomeSubtitle = styled.p`\n  font-size: 16px;\n  color: #666;\n  margin: 0;\n`;\n_c7 = WelcomeSubtitle;\nconst FormCard = styled.div`\n  background-color: white;\n  border-radius: 16px;\n  padding: 30px;\n  width: 100%;\n  max-width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n`;\n_c8 = FormCard;\nconst TabContainer = styled.div`\n  display: flex;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  margin-bottom: 25px;\n  overflow: hidden;\n`;\n_c9 = TabContainer;\nconst Tab = styled.button`\n  flex: 1;\n  padding: 12px;\n  border: none;\n  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};\n  color: ${props => props.active ? 'white' : '#666'};\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n`;\n_c0 = Tab;\nconst FormGroup = styled.div`\n  margin-bottom: 20px;\n`;\n_c1 = FormGroup;\nconst Label = styled.label`\n  display: block;\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  font-weight: 500;\n`;\n_c10 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 16px;\n  background-color: #f8f9fa;\n  transition: border-color 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n  }\n\n  &::placeholder {\n    color: #999;\n  }\n`;\n_c11 = Input;\nconst PasswordContainer = styled.div`\n  position: relative;\n`;\n_c12 = PasswordContainer;\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 18px;\n  color: #666;\n`;\n_c13 = PasswordToggle;\nconst PasswordStrength = styled.div`\n  margin-top: 8px;\n  height: 4px;\n  background-color: #e0e0e0;\n  border-radius: 2px;\n  overflow: hidden;\n`;\n_c14 = PasswordStrength;\nconst StrengthBar = styled.div`\n  height: 100%;\n  width: ${props => props.strength}%;\n  background-color: ${props => props.strength < 30 ? '#ff4757' : props.strength < 70 ? '#ffa502' : '#2ed573'};\n  transition: all 0.3s ease;\n`;\n_c15 = StrengthBar;\nconst SubmitButton = styled.button`\n  width: 100%;\n  background-color: #8b5a3c;\n  color: white;\n  border: none;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 10px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #7a4d33;\n  }\n\n  &:disabled {\n    background-color: #ccc;\n    cursor: not-allowed;\n  }\n`;\n_c16 = SubmitButton;\nconst LoginLink = styled.div`\n  text-align: center;\n  margin-top: 20px;\n  font-size: 14px;\n  color: #666;\n`;\n_c17 = LoginLink;\nconst LoginButton = styled.button`\n  background: none;\n  border: none;\n  color: #ff6b35;\n  text-decoration: underline;\n  cursor: pointer;\n  font-size: 14px;\n`;\n_c18 = LoginButton;\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('email');\n  const [formData, setFormData] = useState({\n    email: '',\n    phone: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const {\n    register,\n    isLoading,\n    error\n  } = useAuth();\n\n  // Get referral code from URL\n  const referralCode = searchParams.get('ref');\n  const calculatePasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength += 25;\n    if (/[A-Z]/.test(password)) strength += 25;\n    if (/[0-9]/.test(password)) strength += 25;\n    if (/[^A-Za-z0-9]/.test(password)) strength += 25;\n    return strength;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const registrationData = {\n      ...formData,\n      referralCode: referralCode || undefined\n    };\n\n    // Remove empty fields\n    if (activeTab === 'email') {\n      delete registrationData.phone;\n    } else {\n      delete registrationData.email;\n    }\n    if (!registrationData.email && !registrationData.phone) {\n      alert('Please provide email or phone number');\n      return;\n    }\n    if (!registrationData.password || registrationData.password.length < 6) {\n      alert('Password must be at least 6 characters');\n      return;\n    }\n    const result = await register(registrationData);\n    if (result.success) {\n      navigate('/');\n    } else {\n      alert(result.error || 'Registration failed');\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(RegisterContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Logo, {\n      children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n        children: \"S\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        children: \"scottsdalegolf Mall\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WelcomeText, {\n      children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n        children: \"Welcome to\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WelcomeSubtitle, {\n        children: \"scottsdalegolf Mall\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormCard, {\n      children: [/*#__PURE__*/_jsxDEV(TabContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'email',\n          onClick: () => setActiveTab('email'),\n          children: \"Register by Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'phone',\n          onClick: () => setActiveTab('phone'),\n          children: \"Register by phone\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: activeTab === 'email' ? 'E-mail' : 'Phone'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: activeTab === 'email' ? 'email' : 'tel',\n            name: activeTab === 'email' ? 'email' : 'phone',\n            placeholder: activeTab === 'email' ? 'E-mail' : 'Phone number',\n            value: formData[activeTab === 'email' ? 'email' : 'phone'],\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: showPassword ? 'text' : 'password',\n              name: \"password\",\n              placeholder: \"Enter your password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PasswordToggle, {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              children: showPassword ? '🙈' : '👁️'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordStrength, {\n            children: /*#__PURE__*/_jsxDEV(StrengthBar, {\n              strength: calculatePasswordStrength(formData.password)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {\n          type: \"submit\",\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoginLink, {\n        children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(LoginButton, {\n          onClick: () => navigate('/login'),\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"/xfrhu+19JiL4n3W+V1IDM4FjZE=\", false, function () {\n  return [useNavigate, useSearchParams, useAuth];\n});\n_c19 = Register;\nexport default Register;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"RegisterContainer\");\n$RefreshReg$(_c2, \"Logo\");\n$RefreshReg$(_c3, \"LogoIcon\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"WelcomeText\");\n$RefreshReg$(_c6, \"WelcomeTitle\");\n$RefreshReg$(_c7, \"WelcomeSubtitle\");\n$RefreshReg$(_c8, \"FormCard\");\n$RefreshReg$(_c9, \"TabContainer\");\n$RefreshReg$(_c0, \"Tab\");\n$RefreshReg$(_c1, \"FormGroup\");\n$RefreshReg$(_c10, \"Label\");\n$RefreshReg$(_c11, \"Input\");\n$RefreshReg$(_c12, \"PasswordContainer\");\n$RefreshReg$(_c13, \"PasswordToggle\");\n$RefreshReg$(_c14, \"PasswordStrength\");\n$RefreshReg$(_c15, \"StrengthBar\");\n$RefreshReg$(_c16, \"SubmitButton\");\n$RefreshReg$(_c17, \"LoginLink\");\n$RefreshReg$(_c18, \"LoginButton\");\n$RefreshReg$(_c19, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useSearchParams", "styled", "useAuth", "jsxDEV", "_jsxDEV", "RegisterContainer", "div", "_c", "Logo", "_c2", "LogoIcon", "_c3", "LogoText", "_c4", "WelcomeText", "_c5", "WelcomeTitle", "h1", "_c6", "WelcomeSubtitle", "p", "_c7", "FormCard", "_c8", "TabContainer", "_c9", "Tab", "button", "props", "active", "_c0", "FormGroup", "_c1", "Label", "label", "_c10", "Input", "input", "_c11", "PasswordContainer", "_c12", "PasswordToggle", "_c13", "PasswordStrength", "_c14", "StrengthBar", "strength", "_c15", "SubmitButton", "_c16", "LoginLink", "_c17", "LoginButton", "_c18", "Register", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "email", "phone", "password", "showPassword", "setShowPassword", "navigate", "searchParams", "register", "isLoading", "error", "referralCode", "get", "calculatePasswordStrength", "length", "test", "handleSubmit", "e", "preventDefault", "registrationData", "undefined", "alert", "result", "success", "handleInputChange", "target", "name", "value", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "placeholder", "onChange", "required", "_c19", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\n\nconst RegisterContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 40px;\n`;\n\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 24px;\n`;\n\nconst LogoText = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n`;\n\nconst WelcomeText = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst WelcomeTitle = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 0 5px 0;\n`;\n\nconst WelcomeSubtitle = styled.p`\n  font-size: 16px;\n  color: #666;\n  margin: 0;\n`;\n\nconst FormCard = styled.div`\n  background-color: white;\n  border-radius: 16px;\n  padding: 30px;\n  width: 100%;\n  max-width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n`;\n\nconst TabContainer = styled.div`\n  display: flex;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  margin-bottom: 25px;\n  overflow: hidden;\n`;\n\nconst Tab = styled.button`\n  flex: 1;\n  padding: 12px;\n  border: none;\n  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};\n  color: ${props => props.active ? 'white' : '#666'};\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  font-weight: 500;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 16px;\n  background-color: #f8f9fa;\n  transition: border-color 0.3s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n  }\n\n  &::placeholder {\n    color: #999;\n  }\n`;\n\nconst PasswordContainer = styled.div`\n  position: relative;\n`;\n\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 18px;\n  color: #666;\n`;\n\nconst PasswordStrength = styled.div`\n  margin-top: 8px;\n  height: 4px;\n  background-color: #e0e0e0;\n  border-radius: 2px;\n  overflow: hidden;\n`;\n\nconst StrengthBar = styled.div`\n  height: 100%;\n  width: ${props => props.strength}%;\n  background-color: ${props => \n    props.strength < 30 ? '#ff4757' : \n    props.strength < 70 ? '#ffa502' : '#2ed573'\n  };\n  transition: all 0.3s ease;\n`;\n\nconst SubmitButton = styled.button`\n  width: 100%;\n  background-color: #8b5a3c;\n  color: white;\n  border: none;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 10px;\n  transition: background-color 0.3s ease;\n\n  &:hover {\n    background-color: #7a4d33;\n  }\n\n  &:disabled {\n    background-color: #ccc;\n    cursor: not-allowed;\n  }\n`;\n\nconst LoginLink = styled.div`\n  text-align: center;\n  margin-top: 20px;\n  font-size: 14px;\n  color: #666;\n`;\n\nconst LoginButton = styled.button`\n  background: none;\n  border: none;\n  color: #ff6b35;\n  text-decoration: underline;\n  cursor: pointer;\n  font-size: 14px;\n`;\n\nconst Register = () => {\n  const [activeTab, setActiveTab] = useState('email');\n  const [formData, setFormData] = useState({\n    email: '',\n    phone: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const { register, isLoading, error } = useAuth();\n\n  // Get referral code from URL\n  const referralCode = searchParams.get('ref');\n\n  const calculatePasswordStrength = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength += 25;\n    if (/[A-Z]/.test(password)) strength += 25;\n    if (/[0-9]/.test(password)) strength += 25;\n    if (/[^A-Za-z0-9]/.test(password)) strength += 25;\n    return strength;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    const registrationData = {\n      ...formData,\n      referralCode: referralCode || undefined\n    };\n\n    // Remove empty fields\n    if (activeTab === 'email') {\n      delete registrationData.phone;\n    } else {\n      delete registrationData.email;\n    }\n\n    if (!registrationData.email && !registrationData.phone) {\n      alert('Please provide email or phone number');\n      return;\n    }\n\n    if (!registrationData.password || registrationData.password.length < 6) {\n      alert('Password must be at least 6 characters');\n      return;\n    }\n\n    const result = await register(registrationData);\n\n    if (result.success) {\n      navigate('/');\n    } else {\n      alert(result.error || 'Registration failed');\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <RegisterContainer>\n      <Logo>\n        <LogoIcon>S</LogoIcon>\n        <LogoText>scottsdalegolf Mall</LogoText>\n      </Logo>\n\n      <WelcomeText>\n        <WelcomeTitle>Welcome to</WelcomeTitle>\n        <WelcomeSubtitle>scottsdalegolf Mall</WelcomeSubtitle>\n      </WelcomeText>\n\n      <FormCard>\n        <TabContainer>\n          <Tab \n            active={activeTab === 'email'} \n            onClick={() => setActiveTab('email')}\n          >\n            Register by Email\n          </Tab>\n          <Tab \n            active={activeTab === 'phone'} \n            onClick={() => setActiveTab('phone')}\n          >\n            Register by phone\n          </Tab>\n        </TabContainer>\n\n        <form onSubmit={handleSubmit}>\n          <FormGroup>\n            <Label>{activeTab === 'email' ? 'E-mail' : 'Phone'}</Label>\n            <Input\n              type={activeTab === 'email' ? 'email' : 'tel'}\n              name={activeTab === 'email' ? 'email' : 'phone'}\n              placeholder={activeTab === 'email' ? 'E-mail' : 'Phone number'}\n              value={formData[activeTab === 'email' ? 'email' : 'phone']}\n              onChange={handleInputChange}\n              required\n            />\n          </FormGroup>\n\n          <FormGroup>\n            <Label>Password</Label>\n            <PasswordContainer>\n              <Input\n                type={showPassword ? 'text' : 'password'}\n                name=\"password\"\n                placeholder=\"Enter your password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                required\n              />\n              <PasswordToggle\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? '🙈' : '👁️'}\n              </PasswordToggle>\n            </PasswordContainer>\n            <PasswordStrength>\n              <StrengthBar strength={calculatePasswordStrength(formData.password)} />\n            </PasswordStrength>\n          </FormGroup>\n\n          <SubmitButton type=\"submit\">\n            Sign Up\n          </SubmitButton>\n        </form>\n\n        <LoginLink>\n          Already have an account?{' '}\n          <LoginButton onClick={() => navigate('/login')}>\n            Sign In\n          </LoginButton>\n        </LoginLink>\n      </FormCard>\n    </RegisterContainer>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,iBAAiB,GAAGJ,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,iBAAiB;AAUvB,MAAMG,IAAI,GAAGP,MAAM,CAACK,GAAG;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,IAAI;AAOV,MAAME,QAAQ,GAAGT,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAXID,QAAQ;AAad,MAAME,QAAQ,GAAGX,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,QAAQ;AAMd,MAAME,WAAW,GAAGb,MAAM,CAACK,GAAG;AAC9B;AACA;AACA,CAAC;AAACS,GAAA,GAHID,WAAW;AAKjB,MAAME,YAAY,GAAGf,MAAM,CAACgB,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,eAAe,GAAGlB,MAAM,CAACmB,CAAC;AAChC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,eAAe;AAMrB,MAAMG,QAAQ,GAAGrB,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAPID,QAAQ;AASd,MAAME,YAAY,GAAGvB,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GANID,YAAY;AAQlB,MAAME,GAAG,GAAGzB,MAAM,CAAC0B,MAAM;AACzB;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AACvE,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,OAAO,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIJ,GAAG;AAYT,MAAMK,SAAS,GAAG9B,MAAM,CAACK,GAAG;AAC5B;AACA,CAAC;AAAC0B,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAGhC,MAAM,CAACiC,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANIF,KAAK;AAQX,MAAMG,KAAK,GAAGnC,MAAM,CAACoC,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAjBIF,KAAK;AAmBX,MAAMG,iBAAiB,GAAGtC,MAAM,CAACK,GAAG;AACpC;AACA,CAAC;AAACkC,IAAA,GAFID,iBAAiB;AAIvB,MAAME,cAAc,GAAGxC,MAAM,CAAC0B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,IAAA,GAVID,cAAc;AAYpB,MAAME,gBAAgB,GAAG1C,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GANID,gBAAgB;AAQtB,MAAME,WAAW,GAAG5C,MAAM,CAACK,GAAG;AAC9B;AACA,WAAWsB,KAAK,IAAIA,KAAK,CAACkB,QAAQ;AAClC,sBAAsBlB,KAAK,IACvBA,KAAK,CAACkB,QAAQ,GAAG,EAAE,GAAG,SAAS,GAC/BlB,KAAK,CAACkB,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;AAC/C;AACA,CACC;AAACC,IAAA,GARIF,WAAW;AAUjB,MAAMG,YAAY,GAAG/C,MAAM,CAAC0B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,IAAA,GArBID,YAAY;AAuBlB,MAAME,SAAS,GAAGjD,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GALID,SAAS;AAOf,MAAME,WAAW,GAAGnD,MAAM,CAAC0B,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAPID,WAAW;AASjB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC;IACvC8D,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMmE,QAAQ,GAAGlE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmE,YAAY,CAAC,GAAGlE,eAAe,CAAC,CAAC;EACxC,MAAM;IAAEmE,QAAQ;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGnE,OAAO,CAAC,CAAC;;EAEhD;EACA,MAAMoE,YAAY,GAAGJ,YAAY,CAACK,GAAG,CAAC,KAAK,CAAC;EAE5C,MAAMC,yBAAyB,GAAIV,QAAQ,IAAK;IAC9C,IAAIhB,QAAQ,GAAG,CAAC;IAChB,IAAIgB,QAAQ,CAACW,MAAM,IAAI,CAAC,EAAE3B,QAAQ,IAAI,EAAE;IACxC,IAAI,OAAO,CAAC4B,IAAI,CAACZ,QAAQ,CAAC,EAAEhB,QAAQ,IAAI,EAAE;IAC1C,IAAI,OAAO,CAAC4B,IAAI,CAACZ,QAAQ,CAAC,EAAEhB,QAAQ,IAAI,EAAE;IAC1C,IAAI,cAAc,CAAC4B,IAAI,CAACZ,QAAQ,CAAC,EAAEhB,QAAQ,IAAI,EAAE;IACjD,OAAOA,QAAQ;EACjB,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,gBAAgB,GAAG;MACvB,GAAGpB,QAAQ;MACXY,YAAY,EAAEA,YAAY,IAAIS;IAChC,CAAC;;IAED;IACA,IAAIvB,SAAS,KAAK,OAAO,EAAE;MACzB,OAAOsB,gBAAgB,CAACjB,KAAK;IAC/B,CAAC,MAAM;MACL,OAAOiB,gBAAgB,CAAClB,KAAK;IAC/B;IAEA,IAAI,CAACkB,gBAAgB,CAAClB,KAAK,IAAI,CAACkB,gBAAgB,CAACjB,KAAK,EAAE;MACtDmB,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA,IAAI,CAACF,gBAAgB,CAAChB,QAAQ,IAAIgB,gBAAgB,CAAChB,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MACtEO,KAAK,CAAC,wCAAwC,CAAC;MAC/C;IACF;IAEA,MAAMC,MAAM,GAAG,MAAMd,QAAQ,CAACW,gBAAgB,CAAC;IAE/C,IAAIG,MAAM,CAACC,OAAO,EAAE;MAClBjB,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,MAAM;MACLe,KAAK,CAACC,MAAM,CAACZ,KAAK,IAAI,qBAAqB,CAAC;IAC9C;EACF,CAAC;EAED,MAAMc,iBAAiB,GAAIP,CAAC,IAAK;IAC/BjB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkB,CAAC,CAACQ,MAAM,CAACC,IAAI,GAAGT,CAAC,CAACQ,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,oBACElF,OAAA,CAACC,iBAAiB;IAAAkF,QAAA,gBAChBnF,OAAA,CAACI,IAAI;MAAA+E,QAAA,gBACHnF,OAAA,CAACM,QAAQ;QAAA6E,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACtBvF,OAAA,CAACQ,QAAQ;QAAA2E,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAEPvF,OAAA,CAACU,WAAW;MAAAyE,QAAA,gBACVnF,OAAA,CAACY,YAAY;QAAAuE,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACvCvF,OAAA,CAACe,eAAe;QAAAoE,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAEdvF,OAAA,CAACkB,QAAQ;MAAAiE,QAAA,gBACPnF,OAAA,CAACoB,YAAY;QAAA+D,QAAA,gBACXnF,OAAA,CAACsB,GAAG;UACFG,MAAM,EAAE2B,SAAS,KAAK,OAAQ;UAC9BoC,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,OAAO,CAAE;UAAA8B,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvF,OAAA,CAACsB,GAAG;UACFG,MAAM,EAAE2B,SAAS,KAAK,OAAQ;UAC9BoC,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,OAAO,CAAE;UAAA8B,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEfvF,OAAA;QAAMyF,QAAQ,EAAElB,YAAa;QAAAY,QAAA,gBAC3BnF,OAAA,CAAC2B,SAAS;UAAAwD,QAAA,gBACRnF,OAAA,CAAC6B,KAAK;YAAAsD,QAAA,EAAE/B,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG;UAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3DvF,OAAA,CAACgC,KAAK;YACJ0D,IAAI,EAAEtC,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,KAAM;YAC9C6B,IAAI,EAAE7B,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAQ;YAChDuC,WAAW,EAAEvC,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,cAAe;YAC/D8B,KAAK,EAAE5B,QAAQ,CAACF,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;YAC3DwC,QAAQ,EAAEb,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZvF,OAAA,CAAC2B,SAAS;UAAAwD,QAAA,gBACRnF,OAAA,CAAC6B,KAAK;YAAAsD,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBvF,OAAA,CAACmC,iBAAiB;YAAAgD,QAAA,gBAChBnF,OAAA,CAACgC,KAAK;cACJ0D,IAAI,EAAE/B,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCsB,IAAI,EAAC,UAAU;cACfU,WAAW,EAAC,qBAAqB;cACjCT,KAAK,EAAE5B,QAAQ,CAACI,QAAS;cACzBkC,QAAQ,EAAEb,iBAAkB;cAC5Bc,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFvF,OAAA,CAACqC,cAAc;cACbqD,IAAI,EAAC,QAAQ;cACbF,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAwB,QAAA,EAE7CxB,YAAY,GAAG,IAAI,GAAG;YAAK;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACpBvF,OAAA,CAACuC,gBAAgB;YAAA4C,QAAA,eACfnF,OAAA,CAACyC,WAAW;cAACC,QAAQ,EAAE0B,yBAAyB,CAACd,QAAQ,CAACI,QAAQ;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEZvF,OAAA,CAAC4C,YAAY;UAAC8C,IAAI,EAAC,QAAQ;UAAAP,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEPvF,OAAA,CAAC8C,SAAS;QAAAqC,QAAA,GAAC,0BACe,EAAC,GAAG,eAC5BnF,OAAA,CAACgD,WAAW;UAACwC,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,QAAQ,CAAE;UAAAsB,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAExB,CAAC;AAACpC,EAAA,CA/IID,QAAQ;EAAA,QAQKvD,WAAW,EACLC,eAAe,EACCE,OAAO;AAAA;AAAAgG,IAAA,GAV1C5C,QAAQ;AAiJd,eAAeA,QAAQ;AAAC,IAAA/C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA6C,IAAA;AAAAC,YAAA,CAAA5F,EAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}