const Transaction = require('../models/Transaction');
const User = require('../models/User');

// @desc    Create recharge request
// @route   POST /api/transactions/recharge
// @access  Private
const createRecharge = async (req, res) => {
  try {
    const { amount, paymentMethod } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid recharge amount'
      });
    }
    
    if (!paymentMethod || !paymentMethod.type) {
      return res.status(400).json({
        success: false,
        message: 'Payment method is required'
      });
    }
    
    // Create recharge transaction
    const transaction = Transaction.createRecharge(req.user.id, amount, paymentMethod);
    await transaction.save();
    
    // Emit real-time notification
    if (req.io) {
      req.io.to(`user-${req.user.id}`).emit('recharge-created', {
        transactionId: transaction._id,
        amount: amount,
        reference: transaction.reference,
        status: transaction.status
      });
    }
    
    res.status(201).json({
      success: true,
      message: 'Recharge request created successfully',
      transaction: {
        id: transaction._id,
        reference: transaction.reference,
        amount: transaction.amount,
        status: transaction.status,
        createdAt: transaction.createdAt
      }
    });
  } catch (error) {
    console.error('Create recharge error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating recharge request'
    });
  }
};

// @desc    Create withdrawal request
// @route   POST /api/transactions/withdraw
// @access  Private
const createWithdrawal = async (req, res) => {
  try {
    const { amount, paymentMethod } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid withdrawal amount'
      });
    }
    
    // Check minimum withdrawal amount
    const minWithdrawal = 10; // $10 minimum
    if (amount < minWithdrawal) {
      return res.status(400).json({
        success: false,
        message: `Minimum withdrawal amount is $${minWithdrawal}`
      });
    }
    
    // Check user balance
    const user = await User.findById(req.user.id);
    if (user.balance.total < amount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }
    
    if (!paymentMethod || !paymentMethod.type) {
      return res.status(400).json({
        success: false,
        message: 'Payment method is required'
      });
    }
    
    // Create withdrawal transaction
    const transaction = Transaction.createWithdrawal(req.user.id, amount, paymentMethod);
    await transaction.save();
    
    // Reserve the amount (don't deduct yet, wait for approval)
    // In a real app, you might want to implement a reserved balance
    
    // Emit real-time notification
    if (req.io) {
      req.io.to(`user-${req.user.id}`).emit('withdrawal-created', {
        transactionId: transaction._id,
        amount: amount,
        reference: transaction.reference,
        status: transaction.status
      });
    }
    
    res.status(201).json({
      success: true,
      message: 'Withdrawal request created successfully',
      transaction: {
        id: transaction._id,
        reference: transaction.reference,
        amount: transaction.amount,
        status: transaction.status,
        createdAt: transaction.createdAt
      }
    });
  } catch (error) {
    console.error('Create withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating withdrawal request'
    });
  }
};

// @desc    Get transaction history
// @route   GET /api/transactions/history
// @access  Private
const getTransactionHistory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const type = req.query.type;
    const status = req.query.status;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { user: req.user.id };
    if (type) query.type = type;
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }
    
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('relatedTo.id', 'name');
    
    const total = await Transaction.countDocuments(query);
    
    // Get summary statistics
    const summary = await Transaction.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);
    
    res.status(200).json({
      success: true,
      transactions,
      summary,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get transaction history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting transaction history'
    });
  }
};

// @desc    Get single transaction
// @route   GET /api/transactions/:id
// @access  Private
const getTransaction = async (req, res) => {
  try {
    const transaction = await Transaction.findById(req.params.id)
      .populate('relatedTo.id', 'name')
      .populate('approval.approvedBy', 'email profile.firstName profile.lastName');
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }
    
    // Check if transaction belongs to user
    if (transaction.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this transaction'
      });
    }
    
    res.status(200).json({
      success: true,
      transaction
    });
  } catch (error) {
    console.error('Get transaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting transaction'
    });
  }
};

// @desc    Cancel transaction
// @route   PUT /api/transactions/:id/cancel
// @access  Private
const cancelTransaction = async (req, res) => {
  try {
    const { reason } = req.body;
    
    const transaction = await Transaction.findById(req.params.id);
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }
    
    // Check if transaction belongs to user
    if (transaction.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to cancel this transaction'
      });
    }
    
    // Check if transaction can be cancelled
    if (!['pending', 'processing'].includes(transaction.status)) {
      return res.status(400).json({
        success: false,
        message: 'Transaction cannot be cancelled'
      });
    }
    
    // Cancel the transaction
    await transaction.cancel(reason || 'Cancelled by user');
    
    // Emit real-time notification
    if (req.io) {
      req.io.to(`user-${req.user.id}`).emit('transaction-cancelled', {
        transactionId: transaction._id,
        reference: transaction.reference
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Transaction cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel transaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error cancelling transaction'
    });
  }
};

// @desc    Get financial summary
// @route   GET /api/transactions/summary
// @access  Private
const getFinancialSummary = async (req, res) => {
  try {
    const period = req.query.period || 'month'; // day, week, month, year, all
    
    let dateFilter = {};
    const now = new Date();
    
    switch (period) {
      case 'day':
        dateFilter = { $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()) };
        break;
      case 'week':
        dateFilter = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case 'month':
        dateFilter = { $gte: new Date(now.getFullYear(), now.getMonth(), 1) };
        break;
      case 'year':
        dateFilter = { $gte: new Date(now.getFullYear(), 0, 1) };
        break;
      default:
        dateFilter = {};
    }
    
    const query = { user: req.user.id };
    if (Object.keys(dateFilter).length > 0) {
      query.createdAt = dateFilter;
    }
    
    // Get summary by transaction type
    const summary = await Transaction.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          completedAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'completed'] }, '$amount', 0]
            }
          }
        }
      }
    ]);
    
    // Get current user balance
    const user = await User.findById(req.user.id);
    
    res.status(200).json({
      success: true,
      summary: {
        period,
        currentBalance: user.balance,
        transactions: summary,
        vipLevel: user.vipLevel
      }
    });
  } catch (error) {
    console.error('Get financial summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting financial summary'
    });
  }
};

module.exports = {
  createRecharge,
  createWithdrawal,
  getTransactionHistory,
  getTransaction,
  cancelTransaction,
  getFinancialSummary
};
