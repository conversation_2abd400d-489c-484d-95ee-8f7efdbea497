{"ast": null, "code": "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\"connect\", \"connect_error\", \"disconnect\", \"disconnecting\", \"newListener\", \"removeListener\" // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n  PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n  PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n  PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n  PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n  PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n  PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n  PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n  /**\n   * Encoder constructor\n   *\n   * @param {function} replacer - custom replacer to pass down to JSON.parse\n   */\n  constructor(replacer) {\n    this.replacer = replacer;\n  }\n  /**\n   * Encode a packet as a single string if non-binary, or as a\n   * buffer sequence, depending on packet type.\n   *\n   * @param {Object} obj - packet object\n   */\n  encode(obj) {\n    if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n      if (hasBinary(obj)) {\n        return this.encodeAsBinary({\n          type: obj.type === PacketType.EVENT ? PacketType.BINARY_EVENT : PacketType.BINARY_ACK,\n          nsp: obj.nsp,\n          data: obj.data,\n          id: obj.id\n        });\n      }\n    }\n    return [this.encodeAsString(obj)];\n  }\n  /**\n   * Encode packet as string.\n   */\n  encodeAsString(obj) {\n    // first is type\n    let str = \"\" + obj.type;\n    // attachments if we have them\n    if (obj.type === PacketType.BINARY_EVENT || obj.type === PacketType.BINARY_ACK) {\n      str += obj.attachments + \"-\";\n    }\n    // if we have a namespace other than `/`\n    // we append it followed by a comma `,`\n    if (obj.nsp && \"/\" !== obj.nsp) {\n      str += obj.nsp + \",\";\n    }\n    // immediately followed by the id\n    if (null != obj.id) {\n      str += obj.id;\n    }\n    // json data\n    if (null != obj.data) {\n      str += JSON.stringify(obj.data, this.replacer);\n    }\n    return str;\n  }\n  /**\n   * Encode packet as 'buffer sequence' by removing blobs, and\n   * deconstructing packet into object with placeholders and\n   * a list of buffers.\n   */\n  encodeAsBinary(obj) {\n    const deconstruction = deconstructPacket(obj);\n    const pack = this.encodeAsString(deconstruction.packet);\n    const buffers = deconstruction.buffers;\n    buffers.unshift(pack); // add packet info to beginning of data list\n    return buffers; // write all the buffers\n  }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n  /**\n   * Decoder constructor\n   *\n   * @param {function} reviver - custom reviver to pass down to JSON.stringify\n   */\n  constructor(reviver) {\n    super();\n    this.reviver = reviver;\n  }\n  /**\n   * Decodes an encoded packet string into packet JSON.\n   *\n   * @param {String} obj - encoded packet\n   */\n  add(obj) {\n    let packet;\n    if (typeof obj === \"string\") {\n      if (this.reconstructor) {\n        throw new Error(\"got plaintext data when reconstructing a packet\");\n      }\n      packet = this.decodeString(obj);\n      const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n      if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n        packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n        // binary packet's json\n        this.reconstructor = new BinaryReconstructor(packet);\n        // no attachments, labeled binary but no binary data to follow\n        if (packet.attachments === 0) {\n          super.emitReserved(\"decoded\", packet);\n        }\n      } else {\n        // non-binary full packet\n        super.emitReserved(\"decoded\", packet);\n      }\n    } else if (isBinary(obj) || obj.base64) {\n      // raw binary data\n      if (!this.reconstructor) {\n        throw new Error(\"got binary data when not reconstructing a packet\");\n      } else {\n        packet = this.reconstructor.takeBinaryData(obj);\n        if (packet) {\n          // received final buffer\n          this.reconstructor = null;\n          super.emitReserved(\"decoded\", packet);\n        }\n      }\n    } else {\n      throw new Error(\"Unknown type: \" + obj);\n    }\n  }\n  /**\n   * Decode a packet String (JSON data)\n   *\n   * @param {String} str\n   * @return {Object} packet\n   */\n  decodeString(str) {\n    let i = 0;\n    // look up type\n    const p = {\n      type: Number(str.charAt(0))\n    };\n    if (PacketType[p.type] === undefined) {\n      throw new Error(\"unknown packet type \" + p.type);\n    }\n    // look up attachments if type binary\n    if (p.type === PacketType.BINARY_EVENT || p.type === PacketType.BINARY_ACK) {\n      const start = i + 1;\n      while (str.charAt(++i) !== \"-\" && i != str.length) {}\n      const buf = str.substring(start, i);\n      if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n        throw new Error(\"Illegal attachments\");\n      }\n      p.attachments = Number(buf);\n    }\n    // look up namespace (if any)\n    if (\"/\" === str.charAt(i + 1)) {\n      const start = i + 1;\n      while (++i) {\n        const c = str.charAt(i);\n        if (\",\" === c) break;\n        if (i === str.length) break;\n      }\n      p.nsp = str.substring(start, i);\n    } else {\n      p.nsp = \"/\";\n    }\n    // look up id\n    const next = str.charAt(i + 1);\n    if (\"\" !== next && Number(next) == next) {\n      const start = i + 1;\n      while (++i) {\n        const c = str.charAt(i);\n        if (null == c || Number(c) != c) {\n          --i;\n          break;\n        }\n        if (i === str.length) break;\n      }\n      p.id = Number(str.substring(start, i + 1));\n    }\n    // look up json data\n    if (str.charAt(++i)) {\n      const payload = this.tryParse(str.substr(i));\n      if (Decoder.isPayloadValid(p.type, payload)) {\n        p.data = payload;\n      } else {\n        throw new Error(\"invalid payload\");\n      }\n    }\n    return p;\n  }\n  tryParse(str) {\n    try {\n      return JSON.parse(str, this.reviver);\n    } catch (e) {\n      return false;\n    }\n  }\n  static isPayloadValid(type, payload) {\n    switch (type) {\n      case PacketType.CONNECT:\n        return isObject(payload);\n      case PacketType.DISCONNECT:\n        return payload === undefined;\n      case PacketType.CONNECT_ERROR:\n        return typeof payload === \"string\" || isObject(payload);\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        return Array.isArray(payload) && (typeof payload[0] === \"number\" || typeof payload[0] === \"string\" && RESERVED_EVENTS.indexOf(payload[0]) === -1);\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        return Array.isArray(payload);\n    }\n  }\n  /**\n   * Deallocates a parser's resources\n   */\n  destroy() {\n    if (this.reconstructor) {\n      this.reconstructor.finishedReconstruction();\n      this.reconstructor = null;\n    }\n  }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n  constructor(packet) {\n    this.packet = packet;\n    this.buffers = [];\n    this.reconPack = packet;\n  }\n  /**\n   * Method to be called when binary data received from connection\n   * after a BINARY_EVENT packet.\n   *\n   * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n   * @return {null | Object} returns null if more binary data is expected or\n   *   a reconstructed packet object if all buffers have been received.\n   */\n  takeBinaryData(binData) {\n    this.buffers.push(binData);\n    if (this.buffers.length === this.reconPack.attachments) {\n      // done with buffer list\n      const packet = reconstructPacket(this.reconPack, this.buffers);\n      this.finishedReconstruction();\n      return packet;\n    }\n    return null;\n  }\n  /**\n   * Cleans up binary packet reconstruction variables.\n   */\n  finishedReconstruction() {\n    this.reconPack = null;\n    this.buffers = [];\n  }\n}", "map": {"version": 3, "names": ["Emitter", "deconstructPacket", "reconstructPacket", "isBinary", "hasBinary", "RESERVED_EVENTS", "protocol", "PacketType", "Encoder", "constructor", "replacer", "encode", "obj", "type", "EVENT", "ACK", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "data", "id", "encodeAsString", "str", "attachments", "JSON", "stringify", "deconstruction", "pack", "packet", "buffers", "unshift", "isObject", "value", "Object", "prototype", "toString", "call", "Decoder", "reviver", "add", "reconstructor", "Error", "decodeString", "isBinaryEvent", "BinaryReconstructor", "emit<PERSON><PERSON><PERSON><PERSON>", "base64", "takeBinaryData", "i", "p", "Number", "char<PERSON>t", "undefined", "start", "length", "buf", "substring", "c", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "parse", "e", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "Array", "isArray", "indexOf", "destroy", "finishedReconstruction", "reconPack", "binData", "push"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/node_modules/socket.io-parser/build/esm/index.js"], "sourcesContent": ["import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,8BAA8B;AACtD,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,aAAa;AAClE,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpD;AACA;AACA;AACA,MAAMC,eAAe,GAAG,CACpB,SAAS,EACT,eAAe,EACf,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,CAAE;AAAA,CACrB;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG,CAAC;AACzB,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACjDA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACvDA,UAAU,CAACA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC7CA,UAAU,CAACA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACzCA,UAAU,CAACA,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EAC7DA,UAAU,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC3DA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;AAC3D,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA,OAAO,MAAMC,OAAO,CAAC;EACjB;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACC,GAAG,EAAE;IACR,IAAIA,GAAG,CAACC,IAAI,KAAKN,UAAU,CAACO,KAAK,IAAIF,GAAG,CAACC,IAAI,KAAKN,UAAU,CAACQ,GAAG,EAAE;MAC9D,IAAIX,SAAS,CAACQ,GAAG,CAAC,EAAE;QAChB,OAAO,IAAI,CAACI,cAAc,CAAC;UACvBH,IAAI,EAAED,GAAG,CAACC,IAAI,KAAKN,UAAU,CAACO,KAAK,GAC7BP,UAAU,CAACU,YAAY,GACvBV,UAAU,CAACW,UAAU;UAC3BC,GAAG,EAAEP,GAAG,CAACO,GAAG;UACZC,IAAI,EAAER,GAAG,CAACQ,IAAI;UACdC,EAAE,EAAET,GAAG,CAACS;QACZ,CAAC,CAAC;MACN;IACJ;IACA,OAAO,CAAC,IAAI,CAACC,cAAc,CAACV,GAAG,CAAC,CAAC;EACrC;EACA;AACJ;AACA;EACIU,cAAcA,CAACV,GAAG,EAAE;IAChB;IACA,IAAIW,GAAG,GAAG,EAAE,GAAGX,GAAG,CAACC,IAAI;IACvB;IACA,IAAID,GAAG,CAACC,IAAI,KAAKN,UAAU,CAACU,YAAY,IACpCL,GAAG,CAACC,IAAI,KAAKN,UAAU,CAACW,UAAU,EAAE;MACpCK,GAAG,IAAIX,GAAG,CAACY,WAAW,GAAG,GAAG;IAChC;IACA;IACA;IACA,IAAIZ,GAAG,CAACO,GAAG,IAAI,GAAG,KAAKP,GAAG,CAACO,GAAG,EAAE;MAC5BI,GAAG,IAAIX,GAAG,CAACO,GAAG,GAAG,GAAG;IACxB;IACA;IACA,IAAI,IAAI,IAAIP,GAAG,CAACS,EAAE,EAAE;MAChBE,GAAG,IAAIX,GAAG,CAACS,EAAE;IACjB;IACA;IACA,IAAI,IAAI,IAAIT,GAAG,CAACQ,IAAI,EAAE;MAClBG,GAAG,IAAIE,IAAI,CAACC,SAAS,CAACd,GAAG,CAACQ,IAAI,EAAE,IAAI,CAACV,QAAQ,CAAC;IAClD;IACA,OAAOa,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;EACIP,cAAcA,CAACJ,GAAG,EAAE;IAChB,MAAMe,cAAc,GAAG1B,iBAAiB,CAACW,GAAG,CAAC;IAC7C,MAAMgB,IAAI,GAAG,IAAI,CAACN,cAAc,CAACK,cAAc,CAACE,MAAM,CAAC;IACvD,MAAMC,OAAO,GAAGH,cAAc,CAACG,OAAO;IACtCA,OAAO,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC,CAAC;IACvB,OAAOE,OAAO,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAASE,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,KAAK,CAAC,KAAK,iBAAiB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,OAAO,SAAStC,OAAO,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIS,WAAWA,CAAC8B,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIC,GAAGA,CAAC5B,GAAG,EAAE;IACL,IAAIiB,MAAM;IACV,IAAI,OAAOjB,GAAG,KAAK,QAAQ,EAAE;MACzB,IAAI,IAAI,CAAC6B,aAAa,EAAE;QACpB,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC;MACtE;MACAb,MAAM,GAAG,IAAI,CAACc,YAAY,CAAC/B,GAAG,CAAC;MAC/B,MAAMgC,aAAa,GAAGf,MAAM,CAAChB,IAAI,KAAKN,UAAU,CAACU,YAAY;MAC7D,IAAI2B,aAAa,IAAIf,MAAM,CAAChB,IAAI,KAAKN,UAAU,CAACW,UAAU,EAAE;QACxDW,MAAM,CAAChB,IAAI,GAAG+B,aAAa,GAAGrC,UAAU,CAACO,KAAK,GAAGP,UAAU,CAACQ,GAAG;QAC/D;QACA,IAAI,CAAC0B,aAAa,GAAG,IAAII,mBAAmB,CAAChB,MAAM,CAAC;QACpD;QACA,IAAIA,MAAM,CAACL,WAAW,KAAK,CAAC,EAAE;UAC1B,KAAK,CAACsB,YAAY,CAAC,SAAS,EAAEjB,MAAM,CAAC;QACzC;MACJ,CAAC,MACI;QACD;QACA,KAAK,CAACiB,YAAY,CAAC,SAAS,EAAEjB,MAAM,CAAC;MACzC;IACJ,CAAC,MACI,IAAI1B,QAAQ,CAACS,GAAG,CAAC,IAAIA,GAAG,CAACmC,MAAM,EAAE;MAClC;MACA,IAAI,CAAC,IAAI,CAACN,aAAa,EAAE;QACrB,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;MACvE,CAAC,MACI;QACDb,MAAM,GAAG,IAAI,CAACY,aAAa,CAACO,cAAc,CAACpC,GAAG,CAAC;QAC/C,IAAIiB,MAAM,EAAE;UACR;UACA,IAAI,CAACY,aAAa,GAAG,IAAI;UACzB,KAAK,CAACK,YAAY,CAAC,SAAS,EAAEjB,MAAM,CAAC;QACzC;MACJ;IACJ,CAAC,MACI;MACD,MAAM,IAAIa,KAAK,CAAC,gBAAgB,GAAG9B,GAAG,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+B,YAAYA,CAACpB,GAAG,EAAE;IACd,IAAI0B,CAAC,GAAG,CAAC;IACT;IACA,MAAMC,CAAC,GAAG;MACNrC,IAAI,EAAEsC,MAAM,CAAC5B,GAAG,CAAC6B,MAAM,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD,IAAI7C,UAAU,CAAC2C,CAAC,CAACrC,IAAI,CAAC,KAAKwC,SAAS,EAAE;MAClC,MAAM,IAAIX,KAAK,CAAC,sBAAsB,GAAGQ,CAAC,CAACrC,IAAI,CAAC;IACpD;IACA;IACA,IAAIqC,CAAC,CAACrC,IAAI,KAAKN,UAAU,CAACU,YAAY,IAClCiC,CAAC,CAACrC,IAAI,KAAKN,UAAU,CAACW,UAAU,EAAE;MAClC,MAAMoC,KAAK,GAAGL,CAAC,GAAG,CAAC;MACnB,OAAO1B,GAAG,CAAC6B,MAAM,CAAC,EAAEH,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,IAAI1B,GAAG,CAACgC,MAAM,EAAE,CAAE;MACrD,MAAMC,GAAG,GAAGjC,GAAG,CAACkC,SAAS,CAACH,KAAK,EAAEL,CAAC,CAAC;MACnC,IAAIO,GAAG,IAAIL,MAAM,CAACK,GAAG,CAAC,IAAIjC,GAAG,CAAC6B,MAAM,CAACH,CAAC,CAAC,KAAK,GAAG,EAAE;QAC7C,MAAM,IAAIP,KAAK,CAAC,qBAAqB,CAAC;MAC1C;MACAQ,CAAC,CAAC1B,WAAW,GAAG2B,MAAM,CAACK,GAAG,CAAC;IAC/B;IACA;IACA,IAAI,GAAG,KAAKjC,GAAG,CAAC6B,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3B,MAAMK,KAAK,GAAGL,CAAC,GAAG,CAAC;MACnB,OAAO,EAAEA,CAAC,EAAE;QACR,MAAMS,CAAC,GAAGnC,GAAG,CAAC6B,MAAM,CAACH,CAAC,CAAC;QACvB,IAAI,GAAG,KAAKS,CAAC,EACT;QACJ,IAAIT,CAAC,KAAK1B,GAAG,CAACgC,MAAM,EAChB;MACR;MACAL,CAAC,CAAC/B,GAAG,GAAGI,GAAG,CAACkC,SAAS,CAACH,KAAK,EAAEL,CAAC,CAAC;IACnC,CAAC,MACI;MACDC,CAAC,CAAC/B,GAAG,GAAG,GAAG;IACf;IACA;IACA,MAAMwC,IAAI,GAAGpC,GAAG,CAAC6B,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,EAAE,KAAKU,IAAI,IAAIR,MAAM,CAACQ,IAAI,CAAC,IAAIA,IAAI,EAAE;MACrC,MAAML,KAAK,GAAGL,CAAC,GAAG,CAAC;MACnB,OAAO,EAAEA,CAAC,EAAE;QACR,MAAMS,CAAC,GAAGnC,GAAG,CAAC6B,MAAM,CAACH,CAAC,CAAC;QACvB,IAAI,IAAI,IAAIS,CAAC,IAAIP,MAAM,CAACO,CAAC,CAAC,IAAIA,CAAC,EAAE;UAC7B,EAAET,CAAC;UACH;QACJ;QACA,IAAIA,CAAC,KAAK1B,GAAG,CAACgC,MAAM,EAChB;MACR;MACAL,CAAC,CAAC7B,EAAE,GAAG8B,MAAM,CAAC5B,GAAG,CAACkC,SAAS,CAACH,KAAK,EAAEL,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C;IACA;IACA,IAAI1B,GAAG,CAAC6B,MAAM,CAAC,EAAEH,CAAC,CAAC,EAAE;MACjB,MAAMW,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACtC,GAAG,CAACuC,MAAM,CAACb,CAAC,CAAC,CAAC;MAC5C,IAAIX,OAAO,CAACyB,cAAc,CAACb,CAAC,CAACrC,IAAI,EAAE+C,OAAO,CAAC,EAAE;QACzCV,CAAC,CAAC9B,IAAI,GAAGwC,OAAO;MACpB,CAAC,MACI;QACD,MAAM,IAAIlB,KAAK,CAAC,iBAAiB,CAAC;MACtC;IACJ;IACA,OAAOQ,CAAC;EACZ;EACAW,QAAQA,CAACtC,GAAG,EAAE;IACV,IAAI;MACA,OAAOE,IAAI,CAACuC,KAAK,CAACzC,GAAG,EAAE,IAAI,CAACgB,OAAO,CAAC;IACxC,CAAC,CACD,OAAO0B,CAAC,EAAE;MACN,OAAO,KAAK;IAChB;EACJ;EACA,OAAOF,cAAcA,CAAClD,IAAI,EAAE+C,OAAO,EAAE;IACjC,QAAQ/C,IAAI;MACR,KAAKN,UAAU,CAAC2D,OAAO;QACnB,OAAOlC,QAAQ,CAAC4B,OAAO,CAAC;MAC5B,KAAKrD,UAAU,CAAC4D,UAAU;QACtB,OAAOP,OAAO,KAAKP,SAAS;MAChC,KAAK9C,UAAU,CAAC6D,aAAa;QACzB,OAAO,OAAOR,OAAO,KAAK,QAAQ,IAAI5B,QAAQ,CAAC4B,OAAO,CAAC;MAC3D,KAAKrD,UAAU,CAACO,KAAK;MACrB,KAAKP,UAAU,CAACU,YAAY;QACxB,OAAQoD,KAAK,CAACC,OAAO,CAACV,OAAO,CAAC,KACzB,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC3BvD,eAAe,CAACkE,OAAO,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAE,CAAC;MAC5D,KAAKrD,UAAU,CAACQ,GAAG;MACnB,KAAKR,UAAU,CAACW,UAAU;QACtB,OAAOmD,KAAK,CAACC,OAAO,CAACV,OAAO,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIY,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC/B,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACgC,sBAAsB,CAAC,CAAC;MAC3C,IAAI,CAAChC,aAAa,GAAG,IAAI;IAC7B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,mBAAmB,CAAC;EACtBpC,WAAWA,CAACoB,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC4C,SAAS,GAAG7C,MAAM;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,cAAcA,CAAC2B,OAAO,EAAE;IACpB,IAAI,CAAC7C,OAAO,CAAC8C,IAAI,CAACD,OAAO,CAAC;IAC1B,IAAI,IAAI,CAAC7C,OAAO,CAACyB,MAAM,KAAK,IAAI,CAACmB,SAAS,CAAClD,WAAW,EAAE;MACpD;MACA,MAAMK,MAAM,GAAG3B,iBAAiB,CAAC,IAAI,CAACwE,SAAS,EAAE,IAAI,CAAC5C,OAAO,CAAC;MAC9D,IAAI,CAAC2C,sBAAsB,CAAC,CAAC;MAC7B,OAAO5C,MAAM;IACjB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACI4C,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC5C,OAAO,GAAG,EAAE;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}