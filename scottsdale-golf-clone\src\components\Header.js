import React from 'react';
import styled from 'styled-components';

const HeaderContainer = styled.header`
  background-color: #ffffff;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LogoIcon = styled.div`
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
`;

const LogoText = styled.span`
  font-size: 18px;
  font-weight: 600;
  color: #333;
`;

const LanguageSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
`;

const Globe = styled.span`
  font-size: 16px;
`;

const Header = () => {
  return (
    <HeaderContainer>
      <Logo>
        <LogoIcon>S</LogoIcon>
        <LogoText>scottsdalegolf Mall</LogoText>
      </Logo>
      <LanguageSelector>
        <Globe>🌐</Globe>
        <span>English</span>
      </LanguageSelector>
    </HeaderContainer>
  );
};

export default Header;
