import React from 'react';
import styled from 'styled-components';

const VIPContainer = styled.div`
  padding: 20px;
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
`;

const ProductCard = styled.div`
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 15px;
  position: relative;
`;

const ProductImage = styled.div`
  width: 100%;
  height: 120px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  position: relative;
`;

const ProductName = styled.h3`
  font-size: 14px;
  font-weight: 500;
  margin: 5px 0;
  color: #333;
`;

const ProductPrice = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 5px 0;
`;

const VipBadgeProduct = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ffd700;
  color: #333;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
`;

const LockIcon = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #666;
`;

const FavoriteButton = styled.button`
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #ff6b6b;
`;

const MemberList = styled.div`
  margin-top: 30px;
`;

const MemberListTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
`;

const MemberCard = styled.div`
  background-color: #f0f0f0;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const MemberEmail = styled.div`
  font-size: 14px;
  color: #333;
`;

const MemberVipBadge = styled.span`
  background-color: #ffd700;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 10px;
`;

const MemberAmount = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #333;
`;

const VIP = () => {
  const products = [
    { id: 1, name: 'Golf clubs', price: '$5.86', vipLevel: 'VIP1', icon: '🏌️', locked: true },
    { id: 2, name: 'Golf clubs', price: '$26.00', vipLevel: 'VIP2', icon: '🏌️', locked: true },
    { id: 3, name: 'Golf clubs', price: '$59.00', vipLevel: 'VIP3', icon: '🏌️', locked: true },
    { id: 4, name: 'Golf clubs', price: '$117.00', vipLevel: 'VIP4', icon: '🏌️', locked: true },
    { id: 5, name: 'Golf clubs', price: '$278.00', vipLevel: 'VIP5', icon: '🏌️', locked: true },
    { id: 6, name: 'Golf clubs', price: '$536.00', vipLevel: 'VIP6', icon: '🏌️', locked: true },
    { id: 7, name: 'Golf clubs', price: '$1,350.00', vipLevel: 'VIP7', icon: '🏌️', locked: true },
    { id: 8, name: 'Golf clubs', price: '$2,350.00', vipLevel: 'VIP8', icon: '🏌️', locked: true },
    { id: 9, name: 'Golf clubs', price: '$4,900.00', vipLevel: 'VIP9', icon: '🏌️', locked: true },
    { id: 10, name: 'Golf clubs', price: '$16,500.00', vipLevel: 'VIP10', icon: '🏌️', locked: true },
  ];

  const members = [
    { email: 'p***@outlook.com', vipLevel: 'VIP5', amount: '+$3,180.00' },
    { email: 'h***@gmail.com', vipLevel: 'VIP2', amount: '+$56.00' },
    { email: 'u***@gmail.com', vipLevel: 'VIP7', amount: '+$9,450.00' },
  ];

  return (
    <VIPContainer>
      <ProductGrid>
        {products.slice(0, 10).map((product) => (
          <ProductCard key={product.id}>
            <VipBadgeProduct>{product.vipLevel}</VipBadgeProduct>
            <ProductImage>
              {product.icon}
              {product.locked && <LockIcon>🔒</LockIcon>}
            </ProductImage>
            <ProductName>{product.name}</ProductName>
            <ProductPrice>{product.price}</ProductPrice>
            <FavoriteButton>❤️</FavoriteButton>
          </ProductCard>
        ))}
      </ProductGrid>

      <MemberList>
        <MemberListTitle>Member list</MemberListTitle>
        {members.map((member, index) => (
          <MemberCard key={index}>
            <MemberEmail>
              {member.email}
              <MemberVipBadge>{member.vipLevel}</MemberVipBadge>
            </MemberEmail>
            <MemberAmount>{member.amount}</MemberAmount>
          </MemberCard>
        ))}
      </MemberList>
    </VIPContainer>
  );
};

export default VIP;
