{"ast": null, "code": "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n  connect: 1,\n  connect_error: 1,\n  disconnect: 1,\n  disconnecting: 1,\n  // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n  newListener: 1,\n  removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n  /**\n   * `Socket` constructor.\n   */\n  constructor(io, nsp, opts) {\n    super();\n    /**\n     * Whether the socket is currently connected to the server.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.connected); // true\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.connected); // false\n     * });\n     */\n    this.connected = false;\n    /**\n     * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n     * be transmitted by the server.\n     */\n    this.recovered = false;\n    /**\n     * Buffer for packets received before the CONNECT packet\n     */\n    this.receiveBuffer = [];\n    /**\n     * Buffer for packets that will be sent once the socket is connected\n     */\n    this.sendBuffer = [];\n    /**\n     * The queue of packets to be sent with retry in case of failure.\n     *\n     * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n     * @private\n     */\n    this._queue = [];\n    /**\n     * A sequence to generate the ID of the {@link QueuedPacket}.\n     * @private\n     */\n    this._queueSeq = 0;\n    this.ids = 0;\n    /**\n     * A map containing acknowledgement handlers.\n     *\n     * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n     *\n     * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n     * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n     * - `const value = await socket.emitWithAck(\"test\")`\n     *\n     * From those that don't:\n     *\n     * - `socket.emit(\"test\", (value) => { ... });`\n     *\n     * In the first case, the handlers will be called with an error when:\n     *\n     * - the timeout is reached\n     * - the socket gets disconnected\n     *\n     * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n     * an acknowledgement from the server.\n     *\n     * @private\n     */\n    this.acks = {};\n    this.flags = {};\n    this.io = io;\n    this.nsp = nsp;\n    if (opts && opts.auth) {\n      this.auth = opts.auth;\n    }\n    this._opts = Object.assign({}, opts);\n    if (this.io._autoConnect) this.open();\n  }\n  /**\n   * Whether the socket is currently disconnected\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"connect\", () => {\n   *   console.log(socket.disconnected); // false\n   * });\n   *\n   * socket.on(\"disconnect\", () => {\n   *   console.log(socket.disconnected); // true\n   * });\n   */\n  get disconnected() {\n    return !this.connected;\n  }\n  /**\n   * Subscribe to open, close and packet events\n   *\n   * @private\n   */\n  subEvents() {\n    if (this.subs) return;\n    const io = this.io;\n    this.subs = [on(io, \"open\", this.onopen.bind(this)), on(io, \"packet\", this.onpacket.bind(this)), on(io, \"error\", this.onerror.bind(this)), on(io, \"close\", this.onclose.bind(this))];\n  }\n  /**\n   * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n   *\n   * @example\n   * const socket = io();\n   *\n   * console.log(socket.active); // true\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   if (reason === \"io server disconnect\") {\n   *     // the disconnection was initiated by the server, you need to manually reconnect\n   *     console.log(socket.active); // false\n   *   }\n   *   // else the socket will automatically try to reconnect\n   *   console.log(socket.active); // true\n   * });\n   */\n  get active() {\n    return !!this.subs;\n  }\n  /**\n   * \"Opens\" the socket.\n   *\n   * @example\n   * const socket = io({\n   *   autoConnect: false\n   * });\n   *\n   * socket.connect();\n   */\n  connect() {\n    if (this.connected) return this;\n    this.subEvents();\n    if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n    if (\"open\" === this.io._readyState) this.onopen();\n    return this;\n  }\n  /**\n   * Alias for {@link connect()}.\n   */\n  open() {\n    return this.connect();\n  }\n  /**\n   * Sends a `message` event.\n   *\n   * This method mimics the WebSocket.send() method.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n   *\n   * @example\n   * socket.send(\"hello\");\n   *\n   * // this is equivalent to\n   * socket.emit(\"message\", \"hello\");\n   *\n   * @return self\n   */\n  send(...args) {\n    args.unshift(\"message\");\n    this.emit.apply(this, args);\n    return this;\n  }\n  /**\n   * Override `emit`.\n   * If the event is in `events`, it's emitted normally.\n   *\n   * @example\n   * socket.emit(\"hello\", \"world\");\n   *\n   * // all serializable datastructures are supported (no need to call JSON.stringify)\n   * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n   *\n   * // with an acknowledgement from the server\n   * socket.emit(\"hello\", \"world\", (val) => {\n   *   // ...\n   * });\n   *\n   * @return self\n   */\n  emit(ev, ...args) {\n    var _a, _b, _c;\n    if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n      throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n    }\n    args.unshift(ev);\n    if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n      this._addToQueue(args);\n      return this;\n    }\n    const packet = {\n      type: PacketType.EVENT,\n      data: args\n    };\n    packet.options = {};\n    packet.options.compress = this.flags.compress !== false;\n    // event ack callback\n    if (\"function\" === typeof args[args.length - 1]) {\n      const id = this.ids++;\n      const ack = args.pop();\n      this._registerAckCallback(id, ack);\n      packet.id = id;\n    }\n    const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n    const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n    const discardPacket = this.flags.volatile && !isTransportWritable;\n    if (discardPacket) {} else if (isConnected) {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    } else {\n      this.sendBuffer.push(packet);\n    }\n    this.flags = {};\n    return this;\n  }\n  /**\n   * @private\n   */\n  _registerAckCallback(id, ack) {\n    var _a;\n    const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n    if (timeout === undefined) {\n      this.acks[id] = ack;\n      return;\n    }\n    // @ts-ignore\n    const timer = this.io.setTimeoutFn(() => {\n      delete this.acks[id];\n      for (let i = 0; i < this.sendBuffer.length; i++) {\n        if (this.sendBuffer[i].id === id) {\n          this.sendBuffer.splice(i, 1);\n        }\n      }\n      ack.call(this, new Error(\"operation has timed out\"));\n    }, timeout);\n    const fn = (...args) => {\n      // @ts-ignore\n      this.io.clearTimeoutFn(timer);\n      ack.apply(this, args);\n    };\n    fn.withError = true;\n    this.acks[id] = fn;\n  }\n  /**\n   * Emits an event and waits for an acknowledgement\n   *\n   * @example\n   * // without timeout\n   * const response = await socket.emitWithAck(\"hello\", \"world\");\n   *\n   * // with a specific timeout\n   * try {\n   *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n   * } catch (err) {\n   *   // the server did not acknowledge the event in the given delay\n   * }\n   *\n   * @return a Promise that will be fulfilled when the server acknowledges the event\n   */\n  emitWithAck(ev, ...args) {\n    return new Promise((resolve, reject) => {\n      const fn = (arg1, arg2) => {\n        return arg1 ? reject(arg1) : resolve(arg2);\n      };\n      fn.withError = true;\n      args.push(fn);\n      this.emit(ev, ...args);\n    });\n  }\n  /**\n   * Add the packet to the queue.\n   * @param args\n   * @private\n   */\n  _addToQueue(args) {\n    let ack;\n    if (typeof args[args.length - 1] === \"function\") {\n      ack = args.pop();\n    }\n    const packet = {\n      id: this._queueSeq++,\n      tryCount: 0,\n      pending: false,\n      args,\n      flags: Object.assign({\n        fromQueue: true\n      }, this.flags)\n    };\n    args.push((err, ...responseArgs) => {\n      if (packet !== this._queue[0]) {\n        // the packet has already been acknowledged\n        return;\n      }\n      const hasError = err !== null;\n      if (hasError) {\n        if (packet.tryCount > this._opts.retries) {\n          this._queue.shift();\n          if (ack) {\n            ack(err);\n          }\n        }\n      } else {\n        this._queue.shift();\n        if (ack) {\n          ack(null, ...responseArgs);\n        }\n      }\n      packet.pending = false;\n      return this._drainQueue();\n    });\n    this._queue.push(packet);\n    this._drainQueue();\n  }\n  /**\n   * Send the first packet of the queue, and wait for an acknowledgement from the server.\n   * @param force - whether to resend a packet that has not been acknowledged yet\n   *\n   * @private\n   */\n  _drainQueue(force = false) {\n    if (!this.connected || this._queue.length === 0) {\n      return;\n    }\n    const packet = this._queue[0];\n    if (packet.pending && !force) {\n      return;\n    }\n    packet.pending = true;\n    packet.tryCount++;\n    this.flags = packet.flags;\n    this.emit.apply(this, packet.args);\n  }\n  /**\n   * Sends a packet.\n   *\n   * @param packet\n   * @private\n   */\n  packet(packet) {\n    packet.nsp = this.nsp;\n    this.io._packet(packet);\n  }\n  /**\n   * Called upon engine `open`.\n   *\n   * @private\n   */\n  onopen() {\n    if (typeof this.auth == \"function\") {\n      this.auth(data => {\n        this._sendConnectPacket(data);\n      });\n    } else {\n      this._sendConnectPacket(this.auth);\n    }\n  }\n  /**\n   * Sends a CONNECT packet to initiate the Socket.IO session.\n   *\n   * @param data\n   * @private\n   */\n  _sendConnectPacket(data) {\n    this.packet({\n      type: PacketType.CONNECT,\n      data: this._pid ? Object.assign({\n        pid: this._pid,\n        offset: this._lastOffset\n      }, data) : data\n    });\n  }\n  /**\n   * Called upon engine or manager `error`.\n   *\n   * @param err\n   * @private\n   */\n  onerror(err) {\n    if (!this.connected) {\n      this.emitReserved(\"connect_error\", err);\n    }\n  }\n  /**\n   * Called upon engine `close`.\n   *\n   * @param reason\n   * @param description\n   * @private\n   */\n  onclose(reason, description) {\n    this.connected = false;\n    delete this.id;\n    this.emitReserved(\"disconnect\", reason, description);\n    this._clearAcks();\n  }\n  /**\n   * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n   * the server.\n   *\n   * @private\n   */\n  _clearAcks() {\n    Object.keys(this.acks).forEach(id => {\n      const isBuffered = this.sendBuffer.some(packet => String(packet.id) === id);\n      if (!isBuffered) {\n        // note: handlers that do not accept an error as first argument are ignored here\n        const ack = this.acks[id];\n        delete this.acks[id];\n        if (ack.withError) {\n          ack.call(this, new Error(\"socket has been disconnected\"));\n        }\n      }\n    });\n  }\n  /**\n   * Called with socket packet.\n   *\n   * @param packet\n   * @private\n   */\n  onpacket(packet) {\n    const sameNamespace = packet.nsp === this.nsp;\n    if (!sameNamespace) return;\n    switch (packet.type) {\n      case PacketType.CONNECT:\n        if (packet.data && packet.data.sid) {\n          this.onconnect(packet.data.sid, packet.data.pid);\n        } else {\n          this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n        }\n        break;\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        this.onevent(packet);\n        break;\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        this.onack(packet);\n        break;\n      case PacketType.DISCONNECT:\n        this.ondisconnect();\n        break;\n      case PacketType.CONNECT_ERROR:\n        this.destroy();\n        const err = new Error(packet.data.message);\n        // @ts-ignore\n        err.data = packet.data.data;\n        this.emitReserved(\"connect_error\", err);\n        break;\n    }\n  }\n  /**\n   * Called upon a server event.\n   *\n   * @param packet\n   * @private\n   */\n  onevent(packet) {\n    const args = packet.data || [];\n    if (null != packet.id) {\n      args.push(this.ack(packet.id));\n    }\n    if (this.connected) {\n      this.emitEvent(args);\n    } else {\n      this.receiveBuffer.push(Object.freeze(args));\n    }\n  }\n  emitEvent(args) {\n    if (this._anyListeners && this._anyListeners.length) {\n      const listeners = this._anyListeners.slice();\n      for (const listener of listeners) {\n        listener.apply(this, args);\n      }\n    }\n    super.emit.apply(this, args);\n    if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n      this._lastOffset = args[args.length - 1];\n    }\n  }\n  /**\n   * Produces an ack callback to emit with an event.\n   *\n   * @private\n   */\n  ack(id) {\n    const self = this;\n    let sent = false;\n    return function (...args) {\n      // prevent double callbacks\n      if (sent) return;\n      sent = true;\n      self.packet({\n        type: PacketType.ACK,\n        id: id,\n        data: args\n      });\n    };\n  }\n  /**\n   * Called upon a server acknowledgement.\n   *\n   * @param packet\n   * @private\n   */\n  onack(packet) {\n    const ack = this.acks[packet.id];\n    if (typeof ack !== \"function\") {\n      return;\n    }\n    delete this.acks[packet.id];\n    // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n    if (ack.withError) {\n      packet.data.unshift(null);\n    }\n    // @ts-ignore\n    ack.apply(this, packet.data);\n  }\n  /**\n   * Called upon server connect.\n   *\n   * @private\n   */\n  onconnect(id, pid) {\n    this.id = id;\n    this.recovered = pid && this._pid === pid;\n    this._pid = pid; // defined only if connection state recovery is enabled\n    this.connected = true;\n    this.emitBuffered();\n    this.emitReserved(\"connect\");\n    this._drainQueue(true);\n  }\n  /**\n   * Emit buffered events (received and emitted).\n   *\n   * @private\n   */\n  emitBuffered() {\n    this.receiveBuffer.forEach(args => this.emitEvent(args));\n    this.receiveBuffer = [];\n    this.sendBuffer.forEach(packet => {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    });\n    this.sendBuffer = [];\n  }\n  /**\n   * Called upon server disconnect.\n   *\n   * @private\n   */\n  ondisconnect() {\n    this.destroy();\n    this.onclose(\"io server disconnect\");\n  }\n  /**\n   * Called upon forced client/server side disconnections,\n   * this method ensures the manager stops tracking us and\n   * that reconnections don't get triggered for this.\n   *\n   * @private\n   */\n  destroy() {\n    if (this.subs) {\n      // clean subscriptions to avoid reconnections\n      this.subs.forEach(subDestroy => subDestroy());\n      this.subs = undefined;\n    }\n    this.io[\"_destroy\"](this);\n  }\n  /**\n   * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n   *\n   * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   // console.log(reason); prints \"io client disconnect\"\n   * });\n   *\n   * socket.disconnect();\n   *\n   * @return self\n   */\n  disconnect() {\n    if (this.connected) {\n      this.packet({\n        type: PacketType.DISCONNECT\n      });\n    }\n    // remove socket from pool\n    this.destroy();\n    if (this.connected) {\n      // fire events\n      this.onclose(\"io client disconnect\");\n    }\n    return this;\n  }\n  /**\n   * Alias for {@link disconnect()}.\n   *\n   * @return self\n   */\n  close() {\n    return this.disconnect();\n  }\n  /**\n   * Sets the compress flag.\n   *\n   * @example\n   * socket.compress(false).emit(\"hello\");\n   *\n   * @param compress - if `true`, compresses the sending data\n   * @return self\n   */\n  compress(compress) {\n    this.flags.compress = compress;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n   * ready to send messages.\n   *\n   * @example\n   * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n   *\n   * @returns self\n   */\n  get volatile() {\n    this.flags.volatile = true;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n   * given number of milliseconds have elapsed without an acknowledgement from the server:\n   *\n   * @example\n   * socket.timeout(5000).emit(\"my-event\", (err) => {\n   *   if (err) {\n   *     // the server did not acknowledge the event in the given delay\n   *   }\n   * });\n   *\n   * @returns self\n   */\n  timeout(timeout) {\n    this.flags.timeout = timeout;\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * @example\n   * socket.onAny((event, ...args) => {\n   *   console.log(`got ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  onAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n    this._anyListeners.push(listener);\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * @example\n   * socket.prependAny((event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  prependAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n    this._anyListeners.unshift(listener);\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * }\n   *\n   * socket.onAny(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAny(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAny();\n   *\n   * @param listener\n   */\n  offAny(listener) {\n    if (!this._anyListeners) {\n      return this;\n    }\n    if (listener) {\n      const listeners = this._anyListeners;\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyListeners = [];\n    }\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n  listenersAny() {\n    return this._anyListeners || [];\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.onAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  onAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n    this._anyOutgoingListeners.push(listener);\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.prependAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n  prependAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n    this._anyOutgoingListeners.unshift(listener);\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * }\n   *\n   * socket.onAnyOutgoing(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAnyOutgoing(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAnyOutgoing();\n   *\n   * @param [listener] - the catch-all listener (optional)\n   */\n  offAnyOutgoing(listener) {\n    if (!this._anyOutgoingListeners) {\n      return this;\n    }\n    if (listener) {\n      const listeners = this._anyOutgoingListeners;\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyOutgoingListeners = [];\n    }\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n  listenersAnyOutgoing() {\n    return this._anyOutgoingListeners || [];\n  }\n  /**\n   * Notify the listeners for each packet sent\n   *\n   * @param packet\n   *\n   * @private\n   */\n  notifyOutgoingListeners(packet) {\n    if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n      const listeners = this._anyOutgoingListeners.slice();\n      for (const listener of listeners) {\n        listener.apply(this, packet.data);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["PacketType", "on", "Emitter", "RESERVED_EVENTS", "Object", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "removeListener", "Socket", "constructor", "io", "nsp", "opts", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "assign", "_autoConnect", "open", "disconnected", "subEvents", "subs", "onopen", "bind", "onpacket", "onerror", "onclose", "active", "_readyState", "send", "args", "unshift", "emit", "apply", "ev", "_a", "_b", "_c", "hasOwnProperty", "Error", "toString", "retries", "fromQueue", "volatile", "_addToQueue", "packet", "type", "EVENT", "data", "options", "compress", "length", "id", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "transport", "writable", "isConnected", "_hasPingExpired", "discardPacket", "notifyOutgoingListeners", "push", "timeout", "ackTimeout", "undefined", "timer", "setTimeoutFn", "i", "splice", "call", "fn", "clearTimeoutFn", "with<PERSON><PERSON><PERSON>", "emitWithAck", "Promise", "resolve", "reject", "arg1", "arg2", "tryCount", "pending", "err", "responseArgs", "<PERSON><PERSON><PERSON><PERSON>", "shift", "_drainQueue", "force", "_packet", "_sendConnectPacket", "CONNECT", "_pid", "pid", "offset", "_lastOffset", "emit<PERSON><PERSON><PERSON><PERSON>", "reason", "description", "_clearAcks", "keys", "for<PERSON>ach", "isBuffered", "some", "String", "sameNamespace", "sid", "onconnect", "BINARY_EVENT", "onevent", "ACK", "BINARY_ACK", "onack", "DISCONNECT", "ondisconnect", "CONNECT_ERROR", "destroy", "message", "emitEvent", "_anyListeners", "listeners", "slice", "listener", "self", "sent", "emitBuffered", "subDestroy", "close", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/node_modules/socket.io-client/build/esm/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,EAAE,QAAQ,SAAS;AAC5B,SAASC,OAAO,QAAS,8BAA8B;AACvD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC;EAClCC,OAAO,EAAE,CAAC;EACVC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChB;EACAC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE;AACpB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,SAASV,OAAO,CAAC;EAChC;AACJ;AACA;EACIW,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACvB,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACX,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAIC,IAAI,IAAIA,IAAI,CAACU,IAAI,EAAE;MACnB,IAAI,CAACA,IAAI,GAAGV,IAAI,CAACU,IAAI;IACzB;IACA,IAAI,CAACC,KAAK,GAAGvB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAAC;IACpC,IAAI,IAAI,CAACF,EAAE,CAACe,YAAY,EACpB,IAAI,CAACC,IAAI,CAAC,CAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,CAAC,IAAI,CAACd,SAAS;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIe,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACC,IAAI,EACT;IACJ,MAAMnB,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,IAAI,CAACmB,IAAI,GAAG,CACRhC,EAAE,CAACa,EAAE,EAAE,MAAM,EAAE,IAAI,CAACoB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EACtClC,EAAE,CAACa,EAAE,EAAE,QAAQ,EAAE,IAAI,CAACsB,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAC1ClC,EAAE,CAACa,EAAE,EAAE,OAAO,EAAE,IAAI,CAACuB,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,EACxClC,EAAE,CAACa,EAAE,EAAE,OAAO,EAAE,IAAI,CAACwB,OAAO,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC,CAC3C;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAII,MAAMA,CAAA,EAAG;IACT,OAAO,CAAC,CAAC,IAAI,CAACN,IAAI;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACW,SAAS,EACd,OAAO,IAAI;IACf,IAAI,CAACe,SAAS,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,CAAClB,EAAE,CAAC,eAAe,CAAC,EACzB,IAAI,CAACA,EAAE,CAACgB,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,MAAM,KAAK,IAAI,CAAChB,EAAE,CAAC0B,WAAW,EAC9B,IAAI,CAACN,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIJ,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACxB,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImC,IAAIA,CAAC,GAAGC,IAAI,EAAE;IACVA,IAAI,CAACC,OAAO,CAAC,SAAS,CAAC;IACvB,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,IAAIA,CAACE,EAAE,EAAE,GAAGJ,IAAI,EAAE;IACd,IAAIK,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,IAAI9C,eAAe,CAAC+C,cAAc,CAACJ,EAAE,CAAC,EAAE;MACpC,MAAM,IAAIK,KAAK,CAAC,GAAG,GAAGL,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,4BAA4B,CAAC;IACvE;IACAV,IAAI,CAACC,OAAO,CAACG,EAAE,CAAC;IAChB,IAAI,IAAI,CAACnB,KAAK,CAAC0B,OAAO,IAAI,CAAC,IAAI,CAAC5B,KAAK,CAAC6B,SAAS,IAAI,CAAC,IAAI,CAAC7B,KAAK,CAAC8B,QAAQ,EAAE;MACrE,IAAI,CAACC,WAAW,CAACd,IAAI,CAAC;MACtB,OAAO,IAAI;IACf;IACA,MAAMe,MAAM,GAAG;MACXC,IAAI,EAAE1D,UAAU,CAAC2D,KAAK;MACtBC,IAAI,EAAElB;IACV,CAAC;IACDe,MAAM,CAACI,OAAO,GAAG,CAAC,CAAC;IACnBJ,MAAM,CAACI,OAAO,CAACC,QAAQ,GAAG,IAAI,CAACrC,KAAK,CAACqC,QAAQ,KAAK,KAAK;IACvD;IACA,IAAI,UAAU,KAAK,OAAOpB,IAAI,CAACA,IAAI,CAACqB,MAAM,GAAG,CAAC,CAAC,EAAE;MAC7C,MAAMC,EAAE,GAAG,IAAI,CAACzC,GAAG,EAAE;MACrB,MAAM0C,GAAG,GAAGvB,IAAI,CAACwB,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,oBAAoB,CAACH,EAAE,EAAEC,GAAG,CAAC;MAClCR,MAAM,CAACO,EAAE,GAAGA,EAAE;IAClB;IACA,MAAMI,mBAAmB,GAAG,CAACpB,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACjC,EAAE,CAACuD,MAAM,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,SAAS,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,QAAQ;IAC3J,MAAMC,WAAW,GAAG,IAAI,CAACvD,SAAS,IAAI,EAAE,CAACgC,EAAE,GAAG,IAAI,CAACnC,EAAE,CAACuD,MAAM,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,eAAe,CAAC,CAAC,CAAC;IACxH,MAAMC,aAAa,GAAG,IAAI,CAACjD,KAAK,CAAC8B,QAAQ,IAAI,CAACa,mBAAmB;IACjE,IAAIM,aAAa,EAAE,CACnB,CAAC,MACI,IAAIF,WAAW,EAAE;MAClB,IAAI,CAACG,uBAAuB,CAAClB,MAAM,CAAC;MACpC,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACrC,UAAU,CAACwD,IAAI,CAACnB,MAAM,CAAC;IAChC;IACA,IAAI,CAAChC,KAAK,GAAG,CAAC,CAAC;IACf,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACI0C,oBAAoBA,CAACH,EAAE,EAAEC,GAAG,EAAE;IAC1B,IAAIlB,EAAE;IACN,MAAM8B,OAAO,GAAG,CAAC9B,EAAE,GAAG,IAAI,CAACtB,KAAK,CAACoD,OAAO,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACpB,KAAK,CAACmD,UAAU;IAChG,IAAID,OAAO,KAAKE,SAAS,EAAE;MACvB,IAAI,CAACvD,IAAI,CAACwC,EAAE,CAAC,GAAGC,GAAG;MACnB;IACJ;IACA;IACA,MAAMe,KAAK,GAAG,IAAI,CAAClE,EAAE,CAACmE,YAAY,CAAC,MAAM;MACrC,OAAO,IAAI,CAACzD,IAAI,CAACwC,EAAE,CAAC;MACpB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9D,UAAU,CAAC2C,MAAM,EAAEmB,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAAC9D,UAAU,CAAC8D,CAAC,CAAC,CAAClB,EAAE,KAAKA,EAAE,EAAE;UAC9B,IAAI,CAAC5C,UAAU,CAAC+D,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;QAChC;MACJ;MACAjB,GAAG,CAACmB,IAAI,CAAC,IAAI,EAAE,IAAIjC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACxD,CAAC,EAAE0B,OAAO,CAAC;IACX,MAAMQ,EAAE,GAAGA,CAAC,GAAG3C,IAAI,KAAK;MACpB;MACA,IAAI,CAAC5B,EAAE,CAACwE,cAAc,CAACN,KAAK,CAAC;MAC7Bf,GAAG,CAACpB,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACzB,CAAC;IACD2C,EAAE,CAACE,SAAS,GAAG,IAAI;IACnB,IAAI,CAAC/D,IAAI,CAACwC,EAAE,CAAC,GAAGqB,EAAE;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAAC1C,EAAE,EAAE,GAAGJ,IAAI,EAAE;IACrB,OAAO,IAAI+C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,MAAMN,EAAE,GAAGA,CAACO,IAAI,EAAEC,IAAI,KAAK;QACvB,OAAOD,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC,GAAGF,OAAO,CAACG,IAAI,CAAC;MAC9C,CAAC;MACDR,EAAE,CAACE,SAAS,GAAG,IAAI;MACnB7C,IAAI,CAACkC,IAAI,CAACS,EAAE,CAAC;MACb,IAAI,CAACzC,IAAI,CAACE,EAAE,EAAE,GAAGJ,IAAI,CAAC;IAC1B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIc,WAAWA,CAACd,IAAI,EAAE;IACd,IAAIuB,GAAG;IACP,IAAI,OAAOvB,IAAI,CAACA,IAAI,CAACqB,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;MAC7CE,GAAG,GAAGvB,IAAI,CAACwB,GAAG,CAAC,CAAC;IACpB;IACA,MAAMT,MAAM,GAAG;MACXO,EAAE,EAAE,IAAI,CAAC1C,SAAS,EAAE;MACpBwE,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,KAAK;MACdrD,IAAI;MACJjB,KAAK,EAAErB,MAAM,CAACwB,MAAM,CAAC;QAAE0B,SAAS,EAAE;MAAK,CAAC,EAAE,IAAI,CAAC7B,KAAK;IACxD,CAAC;IACDiB,IAAI,CAACkC,IAAI,CAAC,CAACoB,GAAG,EAAE,GAAGC,YAAY,KAAK;MAChC,IAAIxC,MAAM,KAAK,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3B;QACA;MACJ;MACA,MAAM6E,QAAQ,GAAGF,GAAG,KAAK,IAAI;MAC7B,IAAIE,QAAQ,EAAE;QACV,IAAIzC,MAAM,CAACqC,QAAQ,GAAG,IAAI,CAACnE,KAAK,CAAC0B,OAAO,EAAE;UACtC,IAAI,CAAChC,MAAM,CAAC8E,KAAK,CAAC,CAAC;UACnB,IAAIlC,GAAG,EAAE;YACLA,GAAG,CAAC+B,GAAG,CAAC;UACZ;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAAC3E,MAAM,CAAC8E,KAAK,CAAC,CAAC;QACnB,IAAIlC,GAAG,EAAE;UACLA,GAAG,CAAC,IAAI,EAAE,GAAGgC,YAAY,CAAC;QAC9B;MACJ;MACAxC,MAAM,CAACsC,OAAO,GAAG,KAAK;MACtB,OAAO,IAAI,CAACK,WAAW,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAAC/E,MAAM,CAACuD,IAAI,CAACnB,MAAM,CAAC;IACxB,IAAI,CAAC2C,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,WAAWA,CAACC,KAAK,GAAG,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACpF,SAAS,IAAI,IAAI,CAACI,MAAM,CAAC0C,MAAM,KAAK,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMN,MAAM,GAAG,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAIoC,MAAM,CAACsC,OAAO,IAAI,CAACM,KAAK,EAAE;MAC1B;IACJ;IACA5C,MAAM,CAACsC,OAAO,GAAG,IAAI;IACrBtC,MAAM,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAACrE,KAAK,GAAGgC,MAAM,CAAChC,KAAK;IACzB,IAAI,CAACmB,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEY,MAAM,CAACf,IAAI,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,MAAMA,CAACA,MAAM,EAAE;IACXA,MAAM,CAAC1C,GAAG,GAAG,IAAI,CAACA,GAAG;IACrB,IAAI,CAACD,EAAE,CAACwF,OAAO,CAAC7C,MAAM,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIvB,MAAMA,CAAA,EAAG;IACL,IAAI,OAAO,IAAI,CAACR,IAAI,IAAI,UAAU,EAAE;MAChC,IAAI,CAACA,IAAI,CAAEkC,IAAI,IAAK;QAChB,IAAI,CAAC2C,kBAAkB,CAAC3C,IAAI,CAAC;MACjC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC2C,kBAAkB,CAAC,IAAI,CAAC7E,IAAI,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6E,kBAAkBA,CAAC3C,IAAI,EAAE;IACrB,IAAI,CAACH,MAAM,CAAC;MACRC,IAAI,EAAE1D,UAAU,CAACwG,OAAO;MACxB5C,IAAI,EAAE,IAAI,CAAC6C,IAAI,GACTrG,MAAM,CAACwB,MAAM,CAAC;QAAE8E,GAAG,EAAE,IAAI,CAACD,IAAI;QAAEE,MAAM,EAAE,IAAI,CAACC;MAAY,CAAC,EAAEhD,IAAI,CAAC,GACjEA;IACV,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvB,OAAOA,CAAC2D,GAAG,EAAE;IACT,IAAI,CAAC,IAAI,CAAC/E,SAAS,EAAE;MACjB,IAAI,CAAC4F,YAAY,CAAC,eAAe,EAAEb,GAAG,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1D,OAAOA,CAACwE,MAAM,EAAEC,WAAW,EAAE;IACzB,IAAI,CAAC9F,SAAS,GAAG,KAAK;IACtB,OAAO,IAAI,CAAC+C,EAAE;IACd,IAAI,CAAC6C,YAAY,CAAC,YAAY,EAAEC,MAAM,EAAEC,WAAW,CAAC;IACpD,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,UAAUA,CAAA,EAAG;IACT5G,MAAM,CAAC6G,IAAI,CAAC,IAAI,CAACzF,IAAI,CAAC,CAAC0F,OAAO,CAAElD,EAAE,IAAK;MACnC,MAAMmD,UAAU,GAAG,IAAI,CAAC/F,UAAU,CAACgG,IAAI,CAAE3D,MAAM,IAAK4D,MAAM,CAAC5D,MAAM,CAACO,EAAE,CAAC,KAAKA,EAAE,CAAC;MAC7E,IAAI,CAACmD,UAAU,EAAE;QACb;QACA,MAAMlD,GAAG,GAAG,IAAI,CAACzC,IAAI,CAACwC,EAAE,CAAC;QACzB,OAAO,IAAI,CAACxC,IAAI,CAACwC,EAAE,CAAC;QACpB,IAAIC,GAAG,CAACsB,SAAS,EAAE;UACftB,GAAG,CAACmB,IAAI,CAAC,IAAI,EAAE,IAAIjC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC7D;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIf,QAAQA,CAACqB,MAAM,EAAE;IACb,MAAM6D,aAAa,GAAG7D,MAAM,CAAC1C,GAAG,KAAK,IAAI,CAACA,GAAG;IAC7C,IAAI,CAACuG,aAAa,EACd;IACJ,QAAQ7D,MAAM,CAACC,IAAI;MACf,KAAK1D,UAAU,CAACwG,OAAO;QACnB,IAAI/C,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACG,IAAI,CAAC2D,GAAG,EAAE;UAChC,IAAI,CAACC,SAAS,CAAC/D,MAAM,CAACG,IAAI,CAAC2D,GAAG,EAAE9D,MAAM,CAACG,IAAI,CAAC8C,GAAG,CAAC;QACpD,CAAC,MACI;UACD,IAAI,CAACG,YAAY,CAAC,eAAe,EAAE,IAAI1D,KAAK,CAAC,2LAA2L,CAAC,CAAC;QAC9O;QACA;MACJ,KAAKnD,UAAU,CAAC2D,KAAK;MACrB,KAAK3D,UAAU,CAACyH,YAAY;QACxB,IAAI,CAACC,OAAO,CAACjE,MAAM,CAAC;QACpB;MACJ,KAAKzD,UAAU,CAAC2H,GAAG;MACnB,KAAK3H,UAAU,CAAC4H,UAAU;QACtB,IAAI,CAACC,KAAK,CAACpE,MAAM,CAAC;QAClB;MACJ,KAAKzD,UAAU,CAAC8H,UAAU;QACtB,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB;MACJ,KAAK/H,UAAU,CAACgI,aAAa;QACzB,IAAI,CAACC,OAAO,CAAC,CAAC;QACd,MAAMjC,GAAG,GAAG,IAAI7C,KAAK,CAACM,MAAM,CAACG,IAAI,CAACsE,OAAO,CAAC;QAC1C;QACAlC,GAAG,CAACpC,IAAI,GAAGH,MAAM,CAACG,IAAI,CAACA,IAAI;QAC3B,IAAI,CAACiD,YAAY,CAAC,eAAe,EAAEb,GAAG,CAAC;QACvC;IACR;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI0B,OAAOA,CAACjE,MAAM,EAAE;IACZ,MAAMf,IAAI,GAAGe,MAAM,CAACG,IAAI,IAAI,EAAE;IAC9B,IAAI,IAAI,IAAIH,MAAM,CAACO,EAAE,EAAE;MACnBtB,IAAI,CAACkC,IAAI,CAAC,IAAI,CAACX,GAAG,CAACR,MAAM,CAACO,EAAE,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAChB,IAAI,CAACkH,SAAS,CAACzF,IAAI,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACvB,aAAa,CAACyD,IAAI,CAACxE,MAAM,CAACC,MAAM,CAACqC,IAAI,CAAC,CAAC;IAChD;EACJ;EACAyF,SAASA,CAACzF,IAAI,EAAE;IACZ,IAAI,IAAI,CAAC0F,aAAa,IAAI,IAAI,CAACA,aAAa,CAACrE,MAAM,EAAE;MACjD,MAAMsE,SAAS,GAAG,IAAI,CAACD,aAAa,CAACE,KAAK,CAAC,CAAC;MAC5C,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;QAC9BE,QAAQ,CAAC1F,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;MAC9B;IACJ;IACA,KAAK,CAACE,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAC5B,IAAI,IAAI,CAAC+D,IAAI,IAAI/D,IAAI,CAACqB,MAAM,IAAI,OAAOrB,IAAI,CAACA,IAAI,CAACqB,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;MACvE,IAAI,CAAC6C,WAAW,GAAGlE,IAAI,CAACA,IAAI,CAACqB,MAAM,GAAG,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,GAAGA,CAACD,EAAE,EAAE;IACJ,MAAMwE,IAAI,GAAG,IAAI;IACjB,IAAIC,IAAI,GAAG,KAAK;IAChB,OAAO,UAAU,GAAG/F,IAAI,EAAE;MACtB;MACA,IAAI+F,IAAI,EACJ;MACJA,IAAI,GAAG,IAAI;MACXD,IAAI,CAAC/E,MAAM,CAAC;QACRC,IAAI,EAAE1D,UAAU,CAAC2H,GAAG;QACpB3D,EAAE,EAAEA,EAAE;QACNJ,IAAI,EAAElB;MACV,CAAC,CAAC;IACN,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACImF,KAAKA,CAACpE,MAAM,EAAE;IACV,MAAMQ,GAAG,GAAG,IAAI,CAACzC,IAAI,CAACiC,MAAM,CAACO,EAAE,CAAC;IAChC,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;MAC3B;IACJ;IACA,OAAO,IAAI,CAACzC,IAAI,CAACiC,MAAM,CAACO,EAAE,CAAC;IAC3B;IACA,IAAIC,GAAG,CAACsB,SAAS,EAAE;MACf9B,MAAM,CAACG,IAAI,CAACjB,OAAO,CAAC,IAAI,CAAC;IAC7B;IACA;IACAsB,GAAG,CAACpB,KAAK,CAAC,IAAI,EAAEY,MAAM,CAACG,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI4D,SAASA,CAACxD,EAAE,EAAE0C,GAAG,EAAE;IACf,IAAI,CAAC1C,EAAE,GAAGA,EAAE;IACZ,IAAI,CAAC9C,SAAS,GAAGwF,GAAG,IAAI,IAAI,CAACD,IAAI,KAAKC,GAAG;IACzC,IAAI,CAACD,IAAI,GAAGC,GAAG,CAAC,CAAC;IACjB,IAAI,CAACzF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACyH,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC7B,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACT,WAAW,CAAC,IAAI,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIsC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACvH,aAAa,CAAC+F,OAAO,CAAExE,IAAI,IAAK,IAAI,CAACyF,SAAS,CAACzF,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACvB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,CAAC8F,OAAO,CAAEzD,MAAM,IAAK;MAChC,IAAI,CAACkB,uBAAuB,CAAClB,MAAM,CAAC;MACpC,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACrC,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI2G,YAAYA,CAAA,EAAG;IACX,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAAC3F,OAAO,CAAC,sBAAsB,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2F,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAChG,IAAI,EAAE;MACX;MACA,IAAI,CAACA,IAAI,CAACiF,OAAO,CAAEyB,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;MAC/C,IAAI,CAAC1G,IAAI,GAAG8C,SAAS;IACzB;IACA,IAAI,CAACjE,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACS,SAAS,EAAE;MAChB,IAAI,CAACwC,MAAM,CAAC;QAAEC,IAAI,EAAE1D,UAAU,CAAC8H;MAAW,CAAC,CAAC;IAChD;IACA;IACA,IAAI,CAACG,OAAO,CAAC,CAAC;IACd,IAAI,IAAI,CAAChH,SAAS,EAAE;MAChB;MACA,IAAI,CAACqB,OAAO,CAAC,sBAAsB,CAAC;IACxC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIsG,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACpI,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsD,QAAQA,CAACA,QAAQ,EAAE;IACf,IAAI,CAACrC,KAAK,CAACqC,QAAQ,GAAGA,QAAQ;IAC9B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIP,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC9B,KAAK,CAAC8B,QAAQ,GAAG,IAAI;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsB,OAAOA,CAACA,OAAO,EAAE;IACb,IAAI,CAACpD,KAAK,CAACoD,OAAO,GAAGA,OAAO;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgE,KAAKA,CAACN,QAAQ,EAAE;IACZ,IAAI,CAACH,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE;IAC7C,IAAI,CAACA,aAAa,CAACxD,IAAI,CAAC2D,QAAQ,CAAC;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,UAAUA,CAACP,QAAQ,EAAE;IACjB,IAAI,CAACH,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE;IAC7C,IAAI,CAACA,aAAa,CAACzF,OAAO,CAAC4F,QAAQ,CAAC;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,MAAMA,CAACR,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAACH,aAAa,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAIG,QAAQ,EAAE;MACV,MAAMF,SAAS,GAAG,IAAI,CAACD,aAAa;MACpC,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,SAAS,CAACtE,MAAM,EAAEmB,CAAC,EAAE,EAAE;QACvC,IAAIqD,QAAQ,KAAKF,SAAS,CAACnD,CAAC,CAAC,EAAE;UAC3BmD,SAAS,CAAClD,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UACtB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACkD,aAAa,GAAG,EAAE;IAC3B;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIY,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACZ,aAAa,IAAI,EAAE;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,aAAaA,CAACV,QAAQ,EAAE;IACpB,IAAI,CAACW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE;IAC7D,IAAI,CAACA,qBAAqB,CAACtE,IAAI,CAAC2D,QAAQ,CAAC;IACzC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIY,kBAAkBA,CAACZ,QAAQ,EAAE;IACzB,IAAI,CAACW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE;IAC7D,IAAI,CAACA,qBAAqB,CAACvG,OAAO,CAAC4F,QAAQ,CAAC;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,cAAcA,CAACb,QAAQ,EAAE;IACrB,IAAI,CAAC,IAAI,CAACW,qBAAqB,EAAE;MAC7B,OAAO,IAAI;IACf;IACA,IAAIX,QAAQ,EAAE;MACV,MAAMF,SAAS,GAAG,IAAI,CAACa,qBAAqB;MAC5C,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,SAAS,CAACtE,MAAM,EAAEmB,CAAC,EAAE,EAAE;QACvC,IAAIqD,QAAQ,KAAKF,SAAS,CAACnD,CAAC,CAAC,EAAE;UAC3BmD,SAAS,CAAClD,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UACtB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACgE,qBAAqB,GAAG,EAAE;IACnC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIG,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACH,qBAAqB,IAAI,EAAE;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvE,uBAAuBA,CAAClB,MAAM,EAAE;IAC5B,IAAI,IAAI,CAACyF,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAACnF,MAAM,EAAE;MACjE,MAAMsE,SAAS,GAAG,IAAI,CAACa,qBAAqB,CAACZ,KAAK,CAAC,CAAC;MACpD,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;QAC9BE,QAAQ,CAAC1F,KAAK,CAAC,IAAI,EAAEY,MAAM,CAACG,IAAI,CAAC;MACrC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}