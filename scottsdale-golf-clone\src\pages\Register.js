import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../services/AuthContext';

const RegisterContainer = styled.div`
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 40px;
`;

const LogoIcon = styled.div`
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
`;

const LogoText = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #333;
`;

const WelcomeText = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const WelcomeTitle = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
`;

const WelcomeSubtitle = styled.p`
  font-size: 16px;
  color: #666;
  margin: 0;
`;

const FormCard = styled.div`
  background-color: white;
  border-radius: 16px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const TabContainer = styled.div`
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 25px;
  overflow: hidden;
`;

const Tab = styled.button`
  flex: 1;
  padding: 12px;
  border: none;
  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #ff6b35;
  }

  &::placeholder {
    color: #999;
  }
`;

const PasswordContainer = styled.div`
  position: relative;
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #666;
`;

const PasswordStrength = styled.div`
  margin-top: 8px;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
`;

const StrengthBar = styled.div`
  height: 100%;
  width: ${props => props.strength}%;
  background-color: ${props => 
    props.strength < 30 ? '#ff4757' : 
    props.strength < 70 ? '#ffa502' : '#2ed573'
  };
  transition: all 0.3s ease;
`;

const SubmitButton = styled.button`
  width: 100%;
  background-color: #8b5a3c;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #7a4d33;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LoginLink = styled.div`
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
`;

const LoginButton = styled.button`
  background: none;
  border: none;
  color: #ff6b35;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
`;

const Register = () => {
  const [activeTab, setActiveTab] = useState('email');
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { register, isLoading, error } = useAuth();

  // Get referral code from URL
  const referralCode = searchParams.get('ref');

  const calculatePasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const registrationData = {
      ...formData,
      referralCode: referralCode || undefined
    };

    // Remove empty fields
    if (activeTab === 'email') {
      delete registrationData.phone;
    } else {
      delete registrationData.email;
    }

    if (!registrationData.email && !registrationData.phone) {
      alert('Please provide email or phone number');
      return;
    }

    if (!registrationData.password || registrationData.password.length < 6) {
      alert('Password must be at least 6 characters');
      return;
    }

    const result = await register(registrationData);

    if (result.success) {
      navigate('/');
    } else {
      alert(result.error || 'Registration failed');
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <RegisterContainer>
      <Logo>
        <LogoIcon>S</LogoIcon>
        <LogoText>scottsdalegolf Mall</LogoText>
      </Logo>

      <WelcomeText>
        <WelcomeTitle>Welcome to</WelcomeTitle>
        <WelcomeSubtitle>scottsdalegolf Mall</WelcomeSubtitle>
      </WelcomeText>

      <FormCard>
        <TabContainer>
          <Tab 
            active={activeTab === 'email'} 
            onClick={() => setActiveTab('email')}
          >
            Register by Email
          </Tab>
          <Tab 
            active={activeTab === 'phone'} 
            onClick={() => setActiveTab('phone')}
          >
            Register by phone
          </Tab>
        </TabContainer>

        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>{activeTab === 'email' ? 'E-mail' : 'Phone'}</Label>
            <Input
              type={activeTab === 'email' ? 'email' : 'tel'}
              name={activeTab === 'email' ? 'email' : 'phone'}
              placeholder={activeTab === 'email' ? 'E-mail' : 'Phone number'}
              value={formData[activeTab === 'email' ? 'email' : 'phone']}
              onChange={handleInputChange}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Password</Label>
            <PasswordContainer>
              <Input
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleInputChange}
                required
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '🙈' : '👁️'}
              </PasswordToggle>
            </PasswordContainer>
            <PasswordStrength>
              <StrengthBar strength={calculatePasswordStrength(formData.password)} />
            </PasswordStrength>
          </FormGroup>

          <SubmitButton type="submit" disabled={isLoading}>
            {isLoading ? 'Creating Account...' : 'Sign Up'}
          </SubmitButton>

          {error && (
            <div style={{ color: 'red', textAlign: 'center', marginTop: '10px', fontSize: '14px' }}>
              {error}
            </div>
          )}
        </form>

        <LoginLink>
          Already have an account?{' '}
          <LoginButton onClick={() => navigate('/login')}>
            Sign In
          </LoginButton>
        </LoginLink>
      </FormCard>
    </RegisterContainer>
  );
};

export default Register;
