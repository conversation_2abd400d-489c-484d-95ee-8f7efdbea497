const express = require('express');
const {
  getTasks,
  getDailyTasks,
  startTask,
  completeTask,
  getTaskStats
} = require('../controllers/taskController');
const { protect, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting
const taskRateLimit = rateLimit(15 * 60 * 1000, 100); // 100 requests per 15 minutes

// All routes are protected
router.use(protect);
router.use(taskRateLimit);

// Task routes
router.get('/', getTasks);
router.get('/daily', getDailyTasks);
router.get('/stats', getTaskStats);
router.post('/:id/start', startTask);
router.post('/:id/complete', completeTask);

module.exports = router;
