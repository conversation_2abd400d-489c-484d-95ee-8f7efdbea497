import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import styled from 'styled-components';
import './App.css';

// Context and Services
import { AuthProvider, useAuth } from './services/AuthContext';
import socketService from './services/socket';

// Components
import Header from './components/Header';
import BottomNavigation from './components/BottomNavigation';
import LoadingSpinner from './components/LoadingSpinner';
import Home from './pages/Home';
import Task from './pages/Task';
import Team from './pages/Team';
import VIP from './pages/VIP';
import Profile from './pages/Profile';
import Register from './pages/Register';
import Login from './pages/Login';

const AppContainer = styled.div`
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  padding-bottom: 80px; /* Space for bottom navigation */
`;

// Main App Component
const AppContent = () => {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Protected Route Component
  const ProtectedRoute = ({ children }) => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    return isAuthenticated ? children : <Navigate to="/login" replace />;
  };

  // Public Route Component (redirect if authenticated)
  const PublicRoute = ({ children }) => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    return !isAuthenticated ? children : <Navigate to="/" replace />;
  };

  // Connect to socket when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      socketService.connect(user.id);

      // Set up real-time event listeners
      socketService.onTaskCompleted((data) => {
        console.log('Task completed:', data);
        // You can show notifications here
      });

      socketService.onPurchaseSuccess((data) => {
        console.log('Purchase successful:', data);
        // You can show notifications here
      });

      socketService.onNewReferral((data) => {
        console.log('New referral:', data);
        // You can show notifications here
      });
    }

    return () => {
      if (isAuthenticated) {
        socketService.disconnect();
      }
    };
  }, [isAuthenticated, user]);

  return (
    <AppContainer>
      {isAuthenticated && <Header />}
      <MainContent>
        <Routes>
          {/* Protected Routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <Home />
            </ProtectedRoute>
          } />
          <Route path="/task" element={
            <ProtectedRoute>
              <Task />
            </ProtectedRoute>
          } />
          <Route path="/team" element={
            <ProtectedRoute>
              <Team />
            </ProtectedRoute>
          } />
          <Route path="/vip" element={
            <ProtectedRoute>
              <VIP />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />

          {/* Public Routes */}
          <Route path="/register" element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          } />
          <Route path="/login" element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          } />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to={isAuthenticated ? "/" : "/login"} replace />} />
        </Routes>
      </MainContent>
      {isAuthenticated && <BottomNavigation />}
    </AppContainer>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppContent />
      </Router>
    </AuthProvider>
  );
}

export default App;
