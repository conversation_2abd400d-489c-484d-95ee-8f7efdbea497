import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import styled from 'styled-components';
import './App.css';

// Components
import Header from './components/Header';
import BottomNavigation from './components/BottomNavigation';
import Home from './pages/Home';
import Task from './pages/Task';
import Team from './pages/Team';
import VIP from './pages/VIP';
import Profile from './pages/Profile';
import Register from './pages/Register';
import Login from './pages/Login';

const AppContainer = styled.div`
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  padding-bottom: 80px; /* Space for bottom navigation */
`;

function App() {
  return (
    <Router>
      <AppContainer>
        <Header />
        <MainContent>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/task" element={<Task />} />
            <Route path="/team" element={<Team />} />
            <Route path="/vip" element={<VIP />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/register" element={<Register />} />
            <Route path="/login" element={<Login />} />
          </Routes>
        </MainContent>
        <BottomNavigation />
      </AppContainer>
    </Router>
  );
}

export default App;
