import React from 'react';
import styled from 'styled-components';

const ProfileContainer = styled.div`
  padding: 20px;
`;

const BalanceCard = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  margin-bottom: 20px;
`;

const UserEmail = styled.div`
  font-size: 16px;
  margin-bottom: 10px;
`;

const VipBadge = styled.span`
  background-color: #ffd700;
  color: #333;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
`;

const BalanceRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
`;

const BalanceLabel = styled.div`
  font-size: 14px;
  opacity: 0.9;
`;

const BalanceAmount = styled.div`
  font-size: 24px;
  font-weight: bold;
`;

const MenuList = styled.div`
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const MenuIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.bgColor || '#e9ecef'};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
`;

const MenuText = styled.div`
  flex: 1;
  font-size: 16px;
  color: #333;
`;

const MenuArrow = styled.div`
  font-size: 18px;
  color: #999;
`;

const SignOutButton = styled.button`
  width: 100%;
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;

  &:hover {
    background-color: #5a6268;
  }
`;

const Profile = () => {
  const menuItems = [
    { icon: '💰', text: 'Recharge', bgColor: '#e3f2fd' },
    { icon: '💸', text: 'Withdraw', bgColor: '#f3e5f5' },
    { icon: '👤', text: 'Account', bgColor: '#e8f5e8' },
    { icon: '📊', text: 'Financial records', bgColor: '#fff3e0' },
    { icon: '🔒', text: 'Change Password', bgColor: '#fce4ec' },
  ];

  return (
    <ProfileContainer>
      <BalanceCard>
        <UserEmail>
          <EMAIL>
          <br />
          <VipBadge>VIP0</VipBadge>
        </UserEmail>
        
        <BalanceRow>
          <div>
            <BalanceLabel>Total balance (USDT)</BalanceLabel>
            <BalanceAmount>0.19</BalanceAmount>
          </div>
          <div style={{ textAlign: 'right' }}>
            <BalanceLabel>Recharge amount (USDT)</BalanceLabel>
            <BalanceAmount>0.00</BalanceAmount>
          </div>
        </BalanceRow>
      </BalanceCard>

      <MenuList>
        {menuItems.map((item, index) => (
          <MenuItem key={index}>
            <MenuIcon bgColor={item.bgColor}>
              {item.icon}
            </MenuIcon>
            <MenuText>{item.text}</MenuText>
            <MenuArrow>›</MenuArrow>
          </MenuItem>
        ))}
      </MenuList>

      <SignOutButton>
        <span>🚪</span>
        <span>Sign out</span>
      </SignOutButton>
    </ProfileContainer>
  );
};

export default Profile;
