const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    sparse: true,
    unique: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  profile: {
    firstName: String,
    lastName: String,
    avatar: String,
    dateOfBirth: Date,
    country: String,
    city: String
  },
  vipLevel: {
    type: Number,
    default: 0,
    min: 0,
    max: 10
  },
  balance: {
    total: {
      type: Number,
      default: 0.19,
      min: 0
    },
    recharge: {
      type: Number,
      default: 0,
      min: 0
    },
    earnings: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  referral: {
    code: {
      type: String,
      unique: true,
      required: true,
      default: function() {
        return Math.floor(100000 + Math.random() * 900000).toString();
      }
    },
    referredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    referredUsers: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      level: {
        type: Number,
        default: 1
      },
      joinedAt: {
        type: Date,
        default: Date.now
      }
    }],
    totalReferrals: {
      type: Number,
      default: 0
    },
    totalEarnings: {
      type: Number,
      default: 0
    }
  },
  team: {
    size: {
      type: Number,
      default: 0
    },
    totalRecharge: {
      type: Number,
      default: 0
    },
    totalWithdrawal: {
      type: Number,
      default: 0
    },
    newMembers: {
      type: Number,
      default: 0
    },
    firstTimeRecharge: {
      type: Number,
      default: 0
    },
    firstWithdrawal: {
      type: Number,
      default: 0
    }
  },
  tasks: {
    dailyTasks: {
      type: Number,
      default: 0
    },
    completedTasks: {
      type: Number,
      default: 0
    },
    totalEarnings: {
      type: Number,
      default: 0
    },
    lastTaskDate: Date
  },
  status: {
    type: String,
    enum: ['active', 'suspended', 'banned'],
    default: 'active'
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  isPhoneVerified: {
    type: Boolean,
    default: false
  },
  lastLogin: Date,
  loginCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.profile.firstName || ''} ${this.profile.lastName || ''}`.trim();
});

// Generate referral code before saving (backup if default doesn't work)
userSchema.pre('save', async function(next) {
  if (!this.referral || !this.referral.code) {
    if (!this.referral) this.referral = {};
    this.referral.code = Math.floor(100000 + Math.random() * 900000).toString();
  }
  next();
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  const salt = await bcrypt.genSalt(12);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Update login info
userSchema.methods.updateLoginInfo = function() {
  this.lastLogin = new Date();
  this.loginCount += 1;
  return this.save();
};

// Add referral
userSchema.methods.addReferral = function(referredUser, level = 1) {
  this.referral.referredUsers.push({
    user: referredUser._id,
    level: level,
    joinedAt: new Date()
  });
  this.referral.totalReferrals += 1;
  this.team.size += 1;
  this.team.newMembers += 1;
  return this.save();
};

// Update VIP level based on balance
userSchema.methods.updateVipLevel = function() {
  const balance = this.balance.total;
  let newVipLevel = 0;
  
  if (balance >= 16500) newVipLevel = 10;
  else if (balance >= 4900) newVipLevel = 9;
  else if (balance >= 2350) newVipLevel = 8;
  else if (balance >= 1350) newVipLevel = 7;
  else if (balance >= 536) newVipLevel = 6;
  else if (balance >= 278) newVipLevel = 5;
  else if (balance >= 117) newVipLevel = 4;
  else if (balance >= 59) newVipLevel = 3;
  else if (balance >= 26) newVipLevel = 2;
  else if (balance >= 5.86) newVipLevel = 1;
  
  this.vipLevel = newVipLevel;
  return this.save();
};

// Index for better performance
userSchema.index({ email: 1 });
userSchema.index({ 'referral.code': 1 });
userSchema.index({ vipLevel: 1 });
userSchema.index({ createdAt: -1 });

module.exports = mongoose.model('User', userSchema);
