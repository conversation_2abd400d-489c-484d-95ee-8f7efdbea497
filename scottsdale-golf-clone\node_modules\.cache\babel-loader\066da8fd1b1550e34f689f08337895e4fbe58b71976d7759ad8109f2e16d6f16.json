{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Context and Services\nimport { AuthProvider, useAuth } from './services/AuthContext';\nimport socketService from './services/socket';\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n_c = AppContainer;\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n\n// Main App Component\n_c2 = MainContent;\nconst AppContent = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n\n  // Protected Route Component\n  const ProtectedRoute = ({\n    children\n  }) => {\n    if (isLoading) {\n      return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 14\n      }, this);\n    }\n    return isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 41\n    }, this);\n  };\n\n  // Public Route Component (redirect if authenticated)\n  const PublicRoute = ({\n    children\n  }) => {\n    if (isLoading) {\n      return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 14\n      }, this);\n    }\n    return !isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 42\n    }, this);\n  };\n\n  // Connect to socket when user is authenticated\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      socketService.connect(user.id);\n\n      // Set up real-time event listeners\n      socketService.onTaskCompleted(data => {\n        console.log('Task completed:', data);\n        // You can show notifications here\n      });\n      socketService.onPurchaseSuccess(data => {\n        console.log('Purchase successful:', data);\n        // You can show notifications here\n      });\n      socketService.onNewReferral(data => {\n        console.log('New referral:', data);\n        // You can show notifications here\n      });\n    }\n    return () => {\n      if (isAuthenticated) {\n        socketService.disconnect();\n      }\n    };\n  }, [isAuthenticated, user]);\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [isAuthenticated && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/task\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(Task, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/team\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(Team, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/vip\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(VIP, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/profile\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n            children: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n            children: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: isAuthenticated ? \"/\" : \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(BottomNavigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 27\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(AppContent, \"OqpqBuJG9HjevTLIwY2py90eEic=\", false, function () {\n  return [useAuth];\n});\n_c3 = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"AppContent\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "styled", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "socketService", "Header", "BottomNavigation", "LoadingSpinner", "Home", "Task", "Team", "VIP", "Profile", "Register", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "MainContent", "main", "_c2", "A<PERSON><PERSON><PERSON>nt", "_s", "user", "isAuthenticated", "isLoading", "ProtectedRoute", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "PublicRoute", "connect", "id", "onTaskCompleted", "data", "console", "log", "onPurchaseSuccess", "onNewReferral", "disconnect", "path", "element", "_c3", "App", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Context and Services\nimport { AuthProvider, useAuth } from './services/AuthContext';\nimport socketService from './services/socket';\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n\n// Main App Component\nconst AppContent = () => {\n  const { user, isAuthenticated, isLoading } = useAuth();\n\n  // Protected Route Component\n  const ProtectedRoute = ({ children }) => {\n    if (isLoading) {\n      return <LoadingSpinner />;\n    }\n\n    return isAuthenticated ? children : <Navigate to=\"/login\" replace />;\n  };\n\n  // Public Route Component (redirect if authenticated)\n  const PublicRoute = ({ children }) => {\n    if (isLoading) {\n      return <LoadingSpinner />;\n    }\n\n    return !isAuthenticated ? children : <Navigate to=\"/\" replace />;\n  };\n\n  // Connect to socket when user is authenticated\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      socketService.connect(user.id);\n\n      // Set up real-time event listeners\n      socketService.onTaskCompleted((data) => {\n        console.log('Task completed:', data);\n        // You can show notifications here\n      });\n\n      socketService.onPurchaseSuccess((data) => {\n        console.log('Purchase successful:', data);\n        // You can show notifications here\n      });\n\n      socketService.onNewReferral((data) => {\n        console.log('New referral:', data);\n        // You can show notifications here\n      });\n    }\n\n    return () => {\n      if (isAuthenticated) {\n        socketService.disconnect();\n      }\n    };\n  }, [isAuthenticated, user]);\n\n  return (\n    <AppContainer>\n      {isAuthenticated && <Header />}\n      <MainContent>\n        <Routes>\n          {/* Protected Routes */}\n          <Route path=\"/\" element={\n            <ProtectedRoute>\n              <Home />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/task\" element={\n            <ProtectedRoute>\n              <Task />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/team\" element={\n            <ProtectedRoute>\n              <Team />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/vip\" element={\n            <ProtectedRoute>\n              <VIP />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/profile\" element={\n            <ProtectedRoute>\n              <Profile />\n            </ProtectedRoute>\n          } />\n\n          {/* Public Routes */}\n          <Route path=\"/register\" element={\n            <PublicRoute>\n              <Register />\n            </PublicRoute>\n          } />\n          <Route path=\"/login\" element={\n            <PublicRoute>\n              <Login />\n            </PublicRoute>\n          } />\n\n          {/* Catch all route */}\n          <Route path=\"*\" element={<Navigate to={isAuthenticated ? \"/\" : \"/login\"} replace />} />\n        </Routes>\n      </MainContent>\n      {isAuthenticated && <BottomNavigation />}\n    </AppContainer>\n  );\n};\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <AppContent />\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,WAAW;;AAElB;AACA,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;;AAE7C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGhB,MAAM,CAACiB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAGnB,MAAM,CAACoB,IAAI;AAC/B;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GALMF,WAAW;AAMjB,MAAMG,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGxB,OAAO,CAAC,CAAC;;EAEtD;EACA,MAAMyB,cAAc,GAAGA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACvC,IAAIF,SAAS,EAAE;MACb,oBAAOX,OAAA,CAACT,cAAc;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3B;IAEA,OAAOP,eAAe,GAAGG,QAAQ,gBAAGb,OAAA,CAAChB,QAAQ;MAACkC,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtE,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAAC;IAAEP;EAAS,CAAC,KAAK;IACpC,IAAIF,SAAS,EAAE;MACb,oBAAOX,OAAA,CAACT,cAAc;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3B;IAEA,OAAO,CAACP,eAAe,GAAGG,QAAQ,gBAAGb,OAAA,CAAChB,QAAQ;MAACkC,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClE,CAAC;;EAED;EACAtC,SAAS,CAAC,MAAM;IACd,IAAI+B,eAAe,IAAID,IAAI,EAAE;MAC3BrB,aAAa,CAACiC,OAAO,CAACZ,IAAI,CAACa,EAAE,CAAC;;MAE9B;MACAlC,aAAa,CAACmC,eAAe,CAAEC,IAAI,IAAK;QACtCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACpC;MACF,CAAC,CAAC;MAEFpC,aAAa,CAACuC,iBAAiB,CAAEH,IAAI,IAAK;QACxCC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEFpC,aAAa,CAACwC,aAAa,CAAEJ,IAAI,IAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX,IAAId,eAAe,EAAE;QACnBtB,aAAa,CAACyC,UAAU,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAACnB,eAAe,EAAED,IAAI,CAAC,CAAC;EAE3B,oBACET,OAAA,CAACC,YAAY;IAAAY,QAAA,GACVH,eAAe,iBAAIV,OAAA,CAACX,MAAM;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9BjB,OAAA,CAACI,WAAW;MAAAS,QAAA,eACVb,OAAA,CAAClB,MAAM;QAAA+B,QAAA,gBAELb,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,GAAG;UAACC,OAAO,eACrB/B,OAAA,CAACY,cAAc;YAAAC,QAAA,eACbb,OAAA,CAACR,IAAI;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,OAAO;UAACC,OAAO,eACzB/B,OAAA,CAACY,cAAc;YAAAC,QAAA,eACbb,OAAA,CAACP,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,OAAO;UAACC,OAAO,eACzB/B,OAAA,CAACY,cAAc;YAAAC,QAAA,eACbb,OAAA,CAACN,IAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,MAAM;UAACC,OAAO,eACxB/B,OAAA,CAACY,cAAc;YAAAC,QAAA,eACbb,OAAA,CAACL,GAAG;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,UAAU;UAACC,OAAO,eAC5B/B,OAAA,CAACY,cAAc;YAAAC,QAAA,eACbb,OAAA,CAACJ,OAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,WAAW;UAACC,OAAO,eAC7B/B,OAAA,CAACoB,WAAW;YAAAP,QAAA,eACVb,OAAA,CAACH,QAAQ;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACd;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAC1B/B,OAAA,CAACoB,WAAW;YAAAP,QAAA,eACVb,OAAA,CAACF,KAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACd;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGJjB,OAAA,CAACjB,KAAK;UAAC+C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE/B,OAAA,CAAChB,QAAQ;YAACkC,EAAE,EAAER,eAAe,GAAG,GAAG,GAAG,QAAS;YAACS,OAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACbP,eAAe,iBAAIV,OAAA,CAACV,gBAAgB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEnB,CAAC;AAACT,EAAA,CArGID,UAAU;EAAA,QAC+BpB,OAAO;AAAA;AAAA6C,GAAA,GADhDzB,UAAU;AAuGhB,SAAS0B,GAAGA,CAAA,EAAG;EACb,oBACEjC,OAAA,CAACd,YAAY;IAAA2B,QAAA,eACXb,OAAA,CAACnB,MAAM;MAAAgC,QAAA,eACLb,OAAA,CAACO,UAAU;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACiB,GAAA,GARQD,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAA9B,EAAA,EAAAG,GAAA,EAAA0B,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}