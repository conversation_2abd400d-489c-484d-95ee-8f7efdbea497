import axios from 'axios';

// Create axios instance
const API = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
API.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => API.post('/auth/register', userData),
  login: (credentials) => API.post('/auth/login', credentials),
  getMe: () => API.get('/auth/me'),
  updateProfile: (profileData) => API.put('/auth/profile', profileData),
  changePassword: (passwordData) => API.put('/auth/password', passwordData),
  logout: () => API.post('/auth/logout'),
};

// User API
export const userAPI = {
  getBalance: () => API.get('/users/balance'),
  getReferralInfo: () => API.get('/users/referral'),
  getTeamStats: () => API.get('/users/team'),
  getTransactions: (params) => API.get('/users/transactions', { params }),
  getUserStats: () => API.get('/users/stats'),
  getVipMembers: () => API.get('/users/vip-members'),
  updateVipLevel: () => API.put('/users/vip-level'),
};

// Product API
export const productAPI = {
  getProducts: (params) => API.get('/products', { params }),
  getProduct: (id) => API.get(`/products/${id}`),
  purchaseProduct: (id) => API.post(`/products/${id}/purchase`),
  addReview: (id, reviewData) => API.post(`/products/${id}/review`, reviewData),
  getCategories: () => API.get('/products/categories'),
  getFeaturedProducts: () => API.get('/products/featured'),
};

// Task API
export const taskAPI = {
  getTasks: (params) => API.get('/tasks', { params }),
  getDailyTasks: () => API.get('/tasks/daily'),
  getTaskStats: () => API.get('/tasks/stats'),
  startTask: (id) => API.post(`/tasks/${id}/start`),
  completeTask: (id) => API.post(`/tasks/${id}/complete`),
};

// Team API
export const teamAPI = {
  getTeamOverview: () => API.get('/teams/overview'),
  getReferralStats: () => API.get('/teams/referral-stats'),
  getTeamPerformance: (params) => API.get('/teams/performance', { params }),
  getTeamMember: (id) => API.get(`/teams/member/${id}`),
};

// Transaction API
export const transactionAPI = {
  createRecharge: (rechargeData) => API.post('/transactions/recharge', rechargeData),
  createWithdrawal: (withdrawalData) => API.post('/transactions/withdraw', withdrawalData),
  getTransactionHistory: (params) => API.get('/transactions/history', { params }),
  getTransaction: (id) => API.get(`/transactions/${id}`),
  cancelTransaction: (id, reason) => API.put(`/transactions/${id}/cancel`, { reason }),
  getFinancialSummary: (params) => API.get('/transactions/summary', { params }),
};

// Health check
export const healthAPI = {
  check: () => API.get('/health'),
};

export default API;
