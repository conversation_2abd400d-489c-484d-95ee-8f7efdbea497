{"ast": null, "code": "/**\n * React Router DOM v6.3.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { useRef, useState, useLayoutEffect, createElement, forwardRef, useCallback, useMemo } from 'react';\nimport { createBrowserHistory, createHashHistory } from 'history';\nimport { Router, useHref, createPath, useLocation, useResolvedPath, useNavigate } from 'react-router';\nexport { MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, Routes, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, createPath, createRoutesFromChildren, generatePath, matchPath, matchRoutes, parsePath, renderMatches, resolvePath, useHref, useInRouterContext, useLocation, useMatch, useNavigate, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes } from 'react-router';\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst _excluded = [\"onClick\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"];\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} ////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref;\n  let historyRef = useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref2;\n  let historyRef = useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    history\n  } = _ref3;\n  const [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nconst Link = /*#__PURE__*/forwardRef(function LinkWithRef(_ref4, ref) {\n  let {\n      onClick,\n      reloadDocument,\n      replace = false,\n      state,\n      target,\n      to\n    } = _ref4,\n    rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n  let href = useHref(to);\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented && !reloadDocument) {\n      internalOnClick(event);\n    }\n  }\n  return (/*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    createElement(\"a\", _extends({}, rest, {\n      href: href,\n      onClick: handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/forwardRef(function NavLinkWithRef(_ref5, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children\n    } = _ref5,\n    rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n  let location = useLocation();\n  let path = useResolvedPath(to);\n  let locationPathname = location.pathname;\n  let toPathname = path.pathname;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    toPathname = toPathname.toLowerCase();\n  }\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive\n  }) : styleProp;\n  return /*#__PURE__*/createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive\n  }) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n} ////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\n\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to);\n  return useCallback(event => {\n    if (event.button === 0 && (\n    // Ignore everything but left clicks\n    !target || target === \"_self\") &&\n    // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n    ) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here.\n\n      let replace = !!replaceProp || createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = useRef(createSearchParams(defaultInit));\n  let location = useLocation();\n  let searchParams = useMemo(() => {\n    let searchParams = createSearchParams(location.search);\n    for (let key of defaultSearchParamsRef.current.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParamsRef.current.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n    return searchParams;\n  }, [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = useCallback((nextInit, navigateOptions) => {\n    navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n  }, [navigate]);\n  return [searchParams, setSearchParams];\n}\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nexport { BrowserRouter, HashRouter, Link, NavLink, createSearchParams, HistoryRouter as unstable_HistoryRouter, useLinkClickHandler, useSearchParams };", "map": {"version": 3, "names": ["warning", "cond", "message", "console", "warn", "Error", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "basename", "children", "window", "historyRef", "useRef", "current", "createBrowserHistory", "history", "state", "setState", "useState", "action", "location", "useLayoutEffect", "listen", "createElement", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "createHashHistory", "HistoryRouter", "_ref3", "process", "env", "NODE_ENV", "displayName", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "Link", "forwardRef", "LinkWithRef", "_ref4", "ref", "onClick", "reloadDocument", "replace", "target", "to", "rest", "_objectWithoutPropertiesLoose", "_excluded", "href", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "_extends", "NavLink", "NavLinkWithRef", "_ref5", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useLocation", "path", "useResolvedPath", "locationPathname", "pathname", "toPathname", "toLowerCase", "isActive", "startsWith", "char<PERSON>t", "length", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "_temp", "replaceProp", "navigate", "useNavigate", "useCallback", "button", "preventDefault", "createPath", "useSearchParams", "defaultInit", "URLSearchParams", "defaultSearchParamsRef", "createSearchParams", "searchParams", "useMemo", "search", "key", "keys", "has", "getAll", "for<PERSON>ach", "value", "append", "setSearchParams", "nextInit", "navigateOptions", "init", "Array", "isArray", "Object", "reduce", "memo", "concat", "map", "v"], "sources": ["C:\\Users\\<USER>\\Desktop\\Hello\\packages\\react-router-dom\\index.tsx"], "sourcesContent": ["/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type { BrowserHistory, HashHistory, History } from \"history\";\nimport { createBrowserHistory, createHashHistory } from \"history\";\nimport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  resolvePath,\n  renderMatches,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n} from \"react-router\";\nimport type { To } from \"react-router\";\n\nfunction warning(cond: boolean, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// RE-EXPORTS\n////////////////////////////////////////////////////////////////////////////////\n\n// Note: Keep in sync with react-router exports!\nexport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  renderMatches,\n  resolvePath,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n};\n\nexport { NavigationType } from \"react-router\";\nexport type {\n  Hash,\n  Location,\n  Path,\n  To,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigator,\n  OutletProps,\n  Params,\n  PathMatch,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  Pathname,\n  Search,\n  RoutesProps,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n} from \"react-router\";\n\n////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nfunction isModifiedEvent(event: React.MouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  to: To;\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    { onClick, reloadDocument, replace = false, state, target, to, ...rest },\n    ref\n  ) {\n    let href = useHref(to);\n    let internalOnClick = useLinkClickHandler(to, { replace, state, target });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented && !reloadDocument) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={href}\n        onClick={handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: { isActive: boolean }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: { isActive: boolean }) => React.CSSProperties);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let location = useLocation();\n    let path = useResolvedPath(to);\n\n    let locationPathname = location.pathname;\n    let toPathname = path.pathname;\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [classNameProp, isActive ? \"active\" : null]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp({ isActive }) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\" ? children({ isActive }) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to);\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (\n        event.button === 0 && // Ignore everything but left clicks\n        (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n        !isModifiedEvent(event) // Ignore clicks with modifier keys\n      ) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here.\n        let replace =\n          !!replaceProp || createPath(location) === createPath(path);\n\n        navigate(to, { replace, state });\n      }\n    },\n    [location, navigate, path, replaceProp, state, target, to]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(defaultInit?: URLSearchParamsInit) {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n\n  let location = useLocation();\n  let searchParams = React.useMemo(() => {\n    let searchParams = createSearchParams(location.search);\n\n    for (let key of defaultSearchParamsRef.current.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParamsRef.current.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n\n    return searchParams;\n  }, [location.search]);\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback(\n    (\n      nextInit: URLSearchParamsInit,\n      navigateOptions?: { replace?: boolean; state?: any }\n    ) => {\n      navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n    },\n    [navigate]\n  );\n\n  return [searchParams, setSearchParams] as const;\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,SAASA,OAATA,CAAiBC,IAAjB,EAAgCC,OAAhC,EAAuD;EACrD,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAaF,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIG,KAAJ,CAAUH,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOI,CAAP,EAAU;EACb;AACF;AAkFD;AACA;;AAQA;AACA;AACA;AACO,SAASC,aAATA,CAAAC,IAAA,EAIgB;EAAA,IAJO;IAC5BC,QAD4B;IAE5BC,QAF4B;IAG5BC;EAH4B,CAIP,GAAAH,IAAA;EACrB,IAAII,UAAU,GAAGC,MAAA,EAAjB;EACA,IAAID,UAAU,CAACE,OAAX,IAAsB,IAA1B,EAAgC;IAC9BF,UAAU,CAACE,OAAX,GAAqBC,oBAAoB,CAAC;MAAEJ;IAAF,CAAD,CAAzC;EACD;EAED,IAAIK,OAAO,GAAGJ,UAAU,CAACE,OAAzB;EACA,IAAI,CAACG,KAAD,EAAQC,QAAR,IAAoBC,QAAA,CAAe;IACrCC,MAAM,EAAEJ,OAAO,CAACI,MADqB;IAErCC,QAAQ,EAAEL,OAAO,CAACK;EAFmB,CAAf,CAAxB;EAKAC,eAAA,CAAsB,MAAMN,OAAO,CAACO,MAAR,CAAeL,QAAf,CAA5B,EAAsD,CAACF,OAAD,CAAtD;EAEA,oBACEQ,aAAA,CAACC,MAAD;IACEhB,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEW,QAAQ,EAAEJ,KAAK,CAACI,QAHlB;IAIEK,cAAc,EAAET,KAAK,CAACG,MAJxB;IAKEO,SAAS,EAAEX;EALb,EADF;AASD;;AAQD;AACA;AACA;AACA;AACO,SAASY,UAATA,CAAAC,KAAA,EAAqE;EAAA,IAAjD;IAAEpB,QAAF;IAAYC,QAAZ;IAAsBC;EAAtB,CAAiD,GAAAkB,KAAA;EAC1E,IAAIjB,UAAU,GAAGC,MAAA,EAAjB;EACA,IAAID,UAAU,CAACE,OAAX,IAAsB,IAA1B,EAAgC;IAC9BF,UAAU,CAACE,OAAX,GAAqBgB,iBAAiB,CAAC;MAAEnB;IAAF,CAAD,CAAtC;EACD;EAED,IAAIK,OAAO,GAAGJ,UAAU,CAACE,OAAzB;EACA,IAAI,CAACG,KAAD,EAAQC,QAAR,IAAoBC,QAAA,CAAe;IACrCC,MAAM,EAAEJ,OAAO,CAACI,MADqB;IAErCC,QAAQ,EAAEL,OAAO,CAACK;EAFmB,CAAf,CAAxB;EAKAC,eAAA,CAAsB,MAAMN,OAAO,CAACO,MAAR,CAAeL,QAAf,CAA5B,EAAsD,CAACF,OAAD,CAAtD;EAEA,oBACEQ,aAAA,CAACC,MAAD;IACEhB,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEW,QAAQ,EAAEJ,KAAK,CAACI,QAHlB;IAIEK,cAAc,EAAET,KAAK,CAACG,MAJxB;IAKEO,SAAS,EAAEX;EALb,EADF;AASD;;AAQD;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,aAATA,CAAAC,KAAA,EAA4E;EAAA,IAArD;IAAEvB,QAAF;IAAYC,QAAZ;IAAsBM;EAAtB,CAAqD,GAAAgB,KAAA;EAC1E,MAAM,CAACf,KAAD,EAAQC,QAAR,IAAoBC,QAAA,CAAe;IACvCC,MAAM,EAAEJ,OAAO,CAACI,MADuB;IAEvCC,QAAQ,EAAEL,OAAO,CAACK;EAFqB,CAAf,CAA1B;EAKAC,eAAA,CAAsB,MAAMN,OAAO,CAACO,MAAR,CAAeL,QAAf,CAA5B,EAAsD,CAACF,OAAD,CAAtD;EAEA,oBACEQ,aAAA,CAACC,MAAD;IACEhB,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEW,QAAQ,EAAEJ,KAAK,CAACI,QAHlB;IAIEK,cAAc,EAAET,KAAK,CAACG,MAJxB;IAKEO,SAAS,EAAEX;EALb,EADF;AASD;AAED,IAAAiB,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXJ,aAAa,CAACK,WAAd,GAA4B,wBAA5B;AACD;AAID,SAASC,eAATA,CAAyBC,KAAzB,EAAkD;EAChD,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAN,IAAiBD,KAAK,CAACE,MAAvB,IAAiCF,KAAK,CAACG,OAAvC,IAAkDH,KAAK,CAACI,QAA1D,CAAR;AACD;;AAUD;AACA;AACA;MACaC,IAAI,gBAAGC,UAAA,CAClB,SAASC,WAATA,CAAAC,KAAA,EAEEC,GAFF,EAGE;EAAA,IAFA;MAAEC,OAAF;MAAWC,cAAX;MAA2BC,OAAO,GAAG,KAArC;MAA4CjC,KAA5C;MAAmDkC,MAAnD;MAA2DC;IAA3D,CAEA,GAAAN,KAAA;IAFkEO,IAElE,GAAAC,6BAAA,CAAAR,KAAA,EAAAS,SAAA;EACA,IAAIC,IAAI,GAAGC,OAAO,CAACL,EAAD,CAAlB;EACA,IAAIM,eAAe,GAAGC,mBAAmB,CAACP,EAAD,EAAK;IAAEF,OAAF;IAAWjC,KAAX;IAAkBkC;EAAlB,CAAL,CAAzC;EACA,SAASS,WAATA,CACEtB,KADF,EAEE;IACA,IAAIU,OAAJ,EAAaA,OAAO,CAACV,KAAD,CAAP;IACb,IAAI,CAACA,KAAK,CAACuB,gBAAP,IAA2B,CAACZ,cAAhC,EAAgD;MAC9CS,eAAe,CAACpB,KAAD,CAAf;IACD;EACF;EAED;IACE;IACAd,aAAA,MAAAsC,QAAA,KACMT,IADN;MAEEG,IAAI,EAAEA,IAFR;MAGER,OAAO,EAAEY,WAHX;MAIEb,GAAG,EAAEA,GAJP;MAKEI,MAAM,EAAEA;IALV;EAAA;AAQH,CA1BiB;AA6BpB,IAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXQ,IAAI,CAACP,WAAL,GAAmB,MAAnB;AACD;;AAeD;AACA;AACA;MACa2B,OAAO,gBAAGnB,UAAA,CACrB,SAASoB,cAATA,CAAAC,KAAA,EAWElB,GAXF,EAYE;EAAA,IAXA;MACE,gBAAgBmB,eAAe,GAAG,MADpC;MAEEC,aAAa,GAAG,KAFlB;MAGEC,SAAS,EAAEC,aAAa,GAAG,EAH7B;MAIEC,GAAG,GAAG,KAJR;MAKEC,KAAK,EAAEC,SALT;MAMEpB,EANF;MAOE1C;IAPF,CAWA,GAAAuD,KAAA;IAHKZ,IAGL,GAAAC,6BAAA,CAAAW,KAAA,EAAAQ,UAAA;EACA,IAAIpD,QAAQ,GAAGqD,WAAW,EAA1B;EACA,IAAIC,IAAI,GAAGC,eAAe,CAACxB,EAAD,CAA1B;EAEA,IAAIyB,gBAAgB,GAAGxD,QAAQ,CAACyD,QAAhC;EACA,IAAIC,UAAU,GAAGJ,IAAI,CAACG,QAAtB;EACA,IAAI,CAACX,aAAL,EAAoB;IAClBU,gBAAgB,GAAGA,gBAAgB,CAACG,WAAjB,EAAnB;IACAD,UAAU,GAAGA,UAAU,CAACC,WAAX,EAAb;EACD;EAED,IAAIC,QAAQ,GACVJ,gBAAgB,KAAKE,UAArB,IACC,CAACT,GAAD,IACCO,gBAAgB,CAACK,UAAjB,CAA4BH,UAA5B,CADD,IAECF,gBAAgB,CAACM,MAAjB,CAAwBJ,UAAU,CAACK,MAAnC,MAA+C,GAJnD;EAMA,IAAIC,WAAW,GAAGJ,QAAQ,GAAGf,eAAH,GAAqBoB,SAA/C;EAEA,IAAIlB,SAAJ;EACA,IAAI,OAAOC,aAAP,KAAyB,UAA7B,EAAyC;IACvCD,SAAS,GAAGC,aAAa,CAAC;MAAEY;IAAF,CAAD,CAAzB;EACD,CAFD,MAEO;IACL;IACA;IACA;IACA;IACA;IACAb,SAAS,GAAG,CAACC,aAAD,EAAgBY,QAAQ,GAAG,QAAH,GAAc,IAAtC,EACTM,MADS,CACFC,OADE,EAETC,IAFS,CAEJ,GAFI,CAAZ;EAGD;EAED,IAAIlB,KAAK,GACP,OAAOC,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAAC;IAAES;EAAF,CAAD,CAA3C,GAA4DT,SAD9D;EAGA,oBACEhD,aAAA,CAACmB,IAAD,EAAAmB,QAAA,KACMT,IADN;IAEE,gBAAcgC,WAFhB;IAGEjB,SAAS,EAAEA,SAHb;IAIErB,GAAG,EAAEA,GAJP;IAKEwB,KAAK,EAAEA,KALT;IAMEnB,EAAE,EAAEA;EANN,IAQG,OAAO1C,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAAC;IAAEuE;EAAF,CAAD,CAAzC,GAA0DvE,QAR7D,CADF;AAYD,CA7DoB;AAgEvB,IAAAuB,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACX4B,OAAO,CAAC3B,WAAR,GAAsB,SAAtB;AACD;AAGD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACO,SAASuB,mBAATA,CACLP,EADK,EAAAsC,KAAA,EAW6C;EAAA,IATlD;IACEvC,MADF;IAEED,OAAO,EAAEyC,WAFX;IAGE1E;EAHF,CASkD,GAAAyE,KAAA,cAD9C,EAC8C,GAAAA,KAAA;EAClD,IAAIE,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIxE,QAAQ,GAAGqD,WAAW,EAA1B;EACA,IAAIC,IAAI,GAAGC,eAAe,CAACxB,EAAD,CAA1B;EAEA,OAAO0C,WAAA,CACJxD,KAAD,IAA4C;IAC1C,IACEA,KAAK,CAACyD,MAAN,KAAiB,CAAjB;IAAA;IACC,CAAC5C,MAAD,IAAWA,MAAM,KAAK,OADvB;IAAA;IAEA,CAACd,eAAe,CAACC,KAAD,CAHlB;IAAA,EAIE;MACAA,KAAK,CAAC0D,cAAN,GADA;MAIA;;MACA,IAAI9C,OAAO,GACT,CAAC,CAACyC,WAAF,IAAiBM,UAAU,CAAC5E,QAAD,CAAV,KAAyB4E,UAAU,CAACtB,IAAD,CADtD;MAGAiB,QAAQ,CAACxC,EAAD,EAAK;QAAEF,OAAF;QAAWjC;MAAX,CAAL,CAAR;IACD;EACF,CAhBI,EAiBL,CAACI,QAAD,EAAWuE,QAAX,EAAqBjB,IAArB,EAA2BgB,WAA3B,EAAwC1E,KAAxC,EAA+CkC,MAA/C,EAAuDC,EAAvD,CAjBK,CAAP;AAmBD;AAED;AACA;AACA;AACA;;AACO,SAAS8C,eAATA,CAAyBC,WAAzB,EAA4D;EACjElE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAnC,OAAO,CACL,OAAOoG,eAAP,KAA2B,WADtB,EAEL,meAFK,CAAP;EAYA,IAAIC,sBAAsB,GAAGxF,MAAA,CAAayF,kBAAkB,CAACH,WAAD,CAA/B,CAA7B;EAEA,IAAI9E,QAAQ,GAAGqD,WAAW,EAA1B;EACA,IAAI6B,YAAY,GAAGC,OAAA,CAAc,MAAM;IACrC,IAAID,YAAY,GAAGD,kBAAkB,CAACjF,QAAQ,CAACoF,MAAV,CAArC;IAEA,KAAK,IAAIC,GAAT,IAAgBL,sBAAsB,CAACvF,OAAvB,CAA+B6F,IAA/B,EAAhB,EAAuD;MACrD,IAAI,CAACJ,YAAY,CAACK,GAAb,CAAiBF,GAAjB,CAAL,EAA4B;QAC1BL,sBAAsB,CAACvF,OAAvB,CAA+B+F,MAA/B,CAAsCH,GAAtC,EAA2CI,OAA3C,CAAoDC,KAAD,IAAW;UAC5DR,YAAY,CAACS,MAAb,CAAoBN,GAApB,EAAyBK,KAAzB;QACD,CAFD;MAGD;IACF;IAED,OAAOR,YAAP;EACD,CAZkB,EAYhB,CAAClF,QAAQ,CAACoF,MAAV,CAZgB,CAAnB;EAcA,IAAIb,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIoB,eAAe,GAAGnB,WAAA,CACpB,CACEoB,QADF,EAEEC,eAFF,KAGK;IACHvB,QAAQ,CAAC,MAAMU,kBAAkB,CAACY,QAAD,CAAzB,EAAqCC,eAArC,CAAR;EACD,CANmB,EAOpB,CAACvB,QAAD,CAPoB,CAAtB;EAUA,OAAO,CAACW,YAAD,EAAeU,eAAf,CAAP;AACD;;AAUD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASX,kBAATA,CACLc,IADK,EAEY;EAAA,IADjBA,IACiB;IADjBA,IACiB,GADW,EACX;EAAA;EACjB,OAAO,IAAIhB,eAAJ,CACL,OAAOgB,IAAP,KAAgB,QAAhB,IACAC,KAAK,CAACC,OAAN,CAAcF,IAAd,CADA,IAEAA,IAAI,YAAYhB,eAFhB,GAGIgB,IAHJ,GAIIG,MAAM,CAACZ,IAAP,CAAYS,IAAZ,EAAkBI,MAAlB,CAAyB,CAACC,IAAD,EAAOf,GAAP,KAAe;IACtC,IAAIK,KAAK,GAAGK,IAAI,CAACV,GAAD,CAAhB;IACA,OAAOe,IAAI,CAACC,MAAL,CACLL,KAAK,CAACC,OAAN,CAAcP,KAAd,IAAuBA,KAAK,CAACY,GAAN,CAAWC,CAAD,IAAO,CAAClB,GAAD,EAAMkB,CAAN,CAAjB,CAAvB,GAAoD,CAAC,CAAClB,GAAD,EAAMK,KAAN,CAAD,CAD/C,CAAP;EAGD,CALD,EAKG,EALH,CALC,CAAP;AAYD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}