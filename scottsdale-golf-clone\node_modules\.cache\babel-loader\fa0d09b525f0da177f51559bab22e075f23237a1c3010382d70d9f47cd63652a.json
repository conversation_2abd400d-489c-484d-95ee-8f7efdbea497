{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Test if basic React is working\nfunction TestComponent() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"React App is Working!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"If you can see this, React is running correctly.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n\n// Components\n_c = TestComponent;\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n_c2 = AppContainer;\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n_c3 = MainContent;\nfunction App() {\n  // Temporarily return test component to debug\n  // return <TestComponent />;\n\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/task\",\n            element: /*#__PURE__*/_jsxDEV(Task, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/team\",\n            element: /*#__PURE__*/_jsxDEV(Team, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/vip\",\n            element: /*#__PURE__*/_jsxDEV(VIP, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BottomNavigation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"TestComponent\");\n$RefreshReg$(_c2, \"AppContainer\");\n$RefreshReg$(_c3, \"MainContent\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "styled", "TestComponent", "_jsxDEV", "style", "padding", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Header", "BottomNavigation", "Home", "Task", "Team", "VIP", "Profile", "Register", "<PERSON><PERSON>", "jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c2", "MainContent", "main", "_c3", "App", "path", "element", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport styled from 'styled-components';\nimport './App.css';\n\n// Test if basic React is working\nfunction TestComponent() {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1>React App is Working!</h1>\n      <p>If you can see this, React is running correctly.</p>\n    </div>\n  );\n}\n\n// Components\nimport Header from './components/Header';\nimport BottomNavigation from './components/BottomNavigation';\nimport Home from './pages/Home';\nimport Task from './pages/Task';\nimport Team from './pages/Team';\nimport VIP from './pages/VIP';\nimport Profile from './pages/Profile';\nimport Register from './pages/Register';\nimport Login from './pages/Login';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  padding-bottom: 80px; /* Space for bottom navigation */\n`;\n\nfunction App() {\n  // Temporarily return test component to debug\n  // return <TestComponent />;\n\n  return (\n    <Router>\n      <AppContainer>\n        <Header />\n        <MainContent>\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/task\" element={<Task />} />\n            <Route path=\"/team\" element={<Team />} />\n            <Route path=\"/vip\" element={<VIP />} />\n            <Route path=\"/profile\" element={<Profile />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/login\" element={<Login />} />\n          </Routes>\n        </MainContent>\n        <BottomNavigation />\n      </AppContainer>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,WAAW;;AAElB;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,oBACEC,OAAA;IAAKC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDJ,OAAA;MAAAI,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BR,OAAA;MAAAI,QAAA,EAAG;IAAgD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;;AAEA;AAAAC,EAAA,GATSV,aAAa;AAUtB,OAAOW,MAAM,MAAM,qBAAqB;AACxC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAnB,OAAA;AAElC,MAAMoB,YAAY,GAAGtB,MAAM,CAACuB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,WAAW,GAAGzB,MAAM,CAAC0B,IAAI;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,WAAW;AAKjB,SAASG,GAAGA,CAAA,EAAG;EACb;EACA;;EAEA,oBACE1B,OAAA,CAACL,MAAM;IAAAS,QAAA,eACLJ,OAAA,CAACoB,YAAY;MAAAhB,QAAA,gBACXJ,OAAA,CAACU,MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVR,OAAA,CAACuB,WAAW;QAAAnB,QAAA,eACVJ,OAAA,CAACJ,MAAM;UAAAQ,QAAA,gBACLJ,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAE5B,OAAA,CAACY,IAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,OAAO;YAACC,OAAO,eAAE5B,OAAA,CAACa,IAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,OAAO;YAACC,OAAO,eAAE5B,OAAA,CAACc,IAAI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,MAAM;YAACC,OAAO,eAAE5B,OAAA,CAACe,GAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,UAAU;YAACC,OAAO,eAAE5B,OAAA,CAACgB,OAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAE5B,OAAA,CAACiB,QAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDR,OAAA,CAACH,KAAK;YAAC8B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAE5B,OAAA,CAACkB,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdR,OAAA,CAACW,gBAAgB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb;AAACqB,GAAA,GAvBQH,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAjB,EAAA,EAAAa,GAAA,EAAAG,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}