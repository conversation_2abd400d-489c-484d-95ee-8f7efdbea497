const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes - require authentication
const protect = async (req, res, next) => {
  try {
    let token;

    // Check for token in header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from token
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'No user found with this token'
        });
      }

      // Check if user is active
      if (user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: 'User account is not active'
        });
      }

      req.user = user;
      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error in authentication'
    });
  }
};

// Grant access to specific roles/VIP levels
const authorize = (...vipLevels) => {
  return (req, res, next) => {
    if (!vipLevels.includes(req.user.vipLevel)) {
      return res.status(403).json({
        success: false,
        message: `VIP level ${req.user.vipLevel} is not authorized to access this route`
      });
    }
    next();
  };
};

// Check if user has minimum VIP level
const requireVipLevel = (minLevel) => {
  return (req, res, next) => {
    if (req.user.vipLevel < minLevel) {
      return res.status(403).json({
        success: false,
        message: `Minimum VIP level ${minLevel} required. Your level: ${req.user.vipLevel}`
      });
    }
    next();
  };
};

// Check if user has sufficient balance
const requireBalance = (minBalance) => {
  return (req, res, next) => {
    if (req.user.balance.total < minBalance) {
      return res.status(403).json({
        success: false,
        message: `Insufficient balance. Required: ${minBalance} USDT, Available: ${req.user.balance.total} USDT`
      });
    }
    next();
  };
};

// Rate limiting middleware
const rateLimit = (windowMs = 15 * 60 * 1000, max = 100) => {
  const requests = new Map();
  
  return (req, res, next) => {
    const key = req.user ? req.user._id.toString() : req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get or create request log for this key
    if (!requests.has(key)) {
      requests.set(key, []);
    }
    
    const requestLog = requests.get(key);
    
    // Remove old requests outside the window
    const validRequests = requestLog.filter(timestamp => timestamp > windowStart);
    requests.set(key, validRequests);
    
    // Check if limit exceeded
    if (validRequests.length >= max) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
    
    // Add current request
    validRequests.push(now);
    requests.set(key, validRequests);
    
    next();
  };
};

// Admin only access
const adminOnly = (req, res, next) => {
  // For now, we'll use VIP level 10 as admin
  // In production, you might want a separate admin role
  if (req.user.vipLevel < 10) {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
};

// Optional authentication - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (user && user.status === 'active') {
          req.user = user;
        }
      } catch (error) {
        // Token invalid, but continue without user
        req.user = null;
      }
    }

    next();
  } catch (error) {
    next();
  }
};

// Check if user owns resource
const checkOwnership = (resourceModel) => {
  return async (req, res, next) => {
    try {
      const Model = require(`../models/${resourceModel}`);
      const resource = await Model.findById(req.params.id);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: `${resourceModel} not found`
        });
      }
      
      // Check if user owns the resource
      if (resource.user && resource.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to access this resource'
        });
      }
      
      req.resource = resource;
      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Server error checking ownership'
      });
    }
  };
};

module.exports = {
  protect,
  authorize,
  requireVipLevel,
  requireBalance,
  rateLimit,
  adminOnly,
  optionalAuth,
  checkOwnership
};
