import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';

const NavContainer = styled.nav`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  z-index: 100;
`;

const NavItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  color: ${props => props.active ? '#ff6b35' : '#999'};
  transition: color 0.3s ease;
`;

const NavIcon = styled.div`
  font-size: 24px;
  margin-bottom: 4px;
`;

const NavLabel = styled.span`
  font-size: 12px;
  font-weight: 500;
`;

const BottomNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    { path: '/', icon: '🏠', label: 'Home' },
    { path: '/task', icon: '📋', label: 'Task' },
    { path: '/team', icon: '👥', label: 'Team' },
    { path: '/vip', icon: '👑', label: 'VIP' },
    { path: '/profile', icon: '👤', label: 'Me' },
  ];

  return (
    <NavContainer>
      {navItems.map((item) => (
        <NavItem
          key={item.path}
          active={location.pathname === item.path}
          onClick={() => navigate(item.path)}
        >
          <NavIcon>{item.icon}</NavIcon>
          <NavLabel>{item.label}</NavLabel>
        </NavItem>
      ))}
    </NavContainer>
  );
};

export default BottomNavigation;
