{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\pages\\\\Task.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TaskContainer = styled.div`\n  padding: 20px;\n`;\n_c = TaskContainer;\nconst TaskStatsCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n_c2 = TaskStatsCard;\nconst StatsRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 15px;\n`;\n_c3 = StatsRow;\nconst StatItem = styled.div`\n  text-align: center;\n  flex: 1;\n`;\n_c4 = StatItem;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 5px;\n`;\n_c5 = StatLabel;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n`;\n_c6 = StatValue;\nconst ProgressCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n_c7 = ProgressCard;\nconst ProgressTabs = styled.div`\n  display: flex;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n`;\n_c8 = ProgressTabs;\nconst ProgressTab = styled.div`\n  flex: 1;\n  padding: 12px;\n  text-align: center;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};\n  color: ${props => props.active ? 'white' : '#666'};\n`;\n_c9 = ProgressTab;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 40px 20px;\n  color: #999;\n  font-size: 16px;\n`;\n_c0 = EmptyState;\nconst Task = () => {\n  return /*#__PURE__*/_jsxDEV(TaskContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TaskStatsCard, {\n      children: /*#__PURE__*/_jsxDEV(StatsRow, {\n        children: [/*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"All tasks for today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Today's remaining tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProgressCard, {\n      children: [/*#__PURE__*/_jsxDEV(ProgressTabs, {\n        children: [/*#__PURE__*/_jsxDEV(ProgressTab, {\n          active: true,\n          children: \"In progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProgressTab, {\n          children: \"Completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: \"No data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_c1 = Task;\nexport default Task;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"TaskContainer\");\n$RefreshReg$(_c2, \"TaskStatsCard\");\n$RefreshReg$(_c3, \"StatsRow\");\n$RefreshReg$(_c4, \"StatItem\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"StatValue\");\n$RefreshReg$(_c7, \"ProgressCard\");\n$RefreshReg$(_c8, \"ProgressTabs\");\n$RefreshReg$(_c9, \"ProgressTab\");\n$RefreshReg$(_c0, \"EmptyState\");\n$RefreshReg$(_c1, \"Task\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "TaskContainer", "div", "_c", "TaskStatsCard", "_c2", "StatsRow", "_c3", "StatItem", "_c4", "StatLabel", "_c5", "StatValue", "_c6", "ProgressCard", "_c7", "ProgressTabs", "_c8", "ProgressTab", "props", "active", "_c9", "EmptyState", "_c0", "Task", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/pages/Task.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst TaskContainer = styled.div`\n  padding: 20px;\n`;\n\nconst TaskStatsCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n\nconst StatsRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 15px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n  flex: 1;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 5px;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n`;\n\nconst ProgressCard = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n\nconst ProgressTabs = styled.div`\n  display: flex;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n`;\n\nconst ProgressTab = styled.div`\n  flex: 1;\n  padding: 12px;\n  text-align: center;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};\n  color: ${props => props.active ? 'white' : '#666'};\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 40px 20px;\n  color: #999;\n  font-size: 16px;\n`;\n\nconst Task = () => {\n  return (\n    <TaskContainer>\n      <TaskStatsCard>\n        <StatsRow>\n          <StatItem>\n            <StatLabel>All tasks for today</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n          <StatItem>\n            <StatLabel>Today's remaining tasks</StatLabel>\n            <StatValue>0</StatValue>\n          </StatItem>\n        </StatsRow>\n      </TaskStatsCard>\n\n      <ProgressCard>\n        <ProgressTabs>\n          <ProgressTab active>In progress</ProgressTab>\n          <ProgressTab>Completed</ProgressTab>\n        </ProgressTabs>\n        \n        <EmptyState>\n          No data\n        </EmptyState>\n      </ProgressCard>\n    </TaskContainer>\n  );\n};\n\nexport default Task;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA,CAAC;AAACC,EAAA,GAFIF,aAAa;AAInB,MAAMG,aAAa,GAAGN,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,aAAa;AAQnB,MAAME,QAAQ,GAAGR,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGV,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,CAAC;AAACO,GAAA,GAHID,QAAQ;AAKd,MAAME,SAAS,GAAGZ,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGd,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAGhB,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GANID,YAAY;AAQlB,MAAME,YAAY,GAAGlB,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GANID,YAAY;AAQlB,MAAME,WAAW,GAAGpB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBiB,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AACvE,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,OAAO,GAAG,MAAM;AACnD,CAAC;AAACC,GAAA,GATIH,WAAW;AAWjB,MAAMI,UAAU,GAAGxB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,UAAU;AAOhB,MAAME,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACExB,OAAA,CAACC,aAAa;IAAAwB,QAAA,gBACZzB,OAAA,CAACI,aAAa;MAAAqB,QAAA,eACZzB,OAAA,CAACM,QAAQ;QAAAmB,QAAA,gBACPzB,OAAA,CAACQ,QAAQ;UAAAiB,QAAA,gBACPzB,OAAA,CAACU,SAAS;YAAAe,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1C7B,OAAA,CAACY,SAAS;YAAAa,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACX7B,OAAA,CAACQ,QAAQ;UAAAiB,QAAA,gBACPzB,OAAA,CAACU,SAAS;YAAAe,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC9C7B,OAAA,CAACY,SAAS;YAAAa,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEhB7B,OAAA,CAACc,YAAY;MAAAW,QAAA,gBACXzB,OAAA,CAACgB,YAAY;QAAAS,QAAA,gBACXzB,OAAA,CAACkB,WAAW;UAACE,MAAM;UAAAK,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7C7B,OAAA,CAACkB,WAAW;UAAAO,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEf7B,OAAA,CAACsB,UAAU;QAAAG,QAAA,EAAC;MAEZ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACC,GAAA,GA5BIN,IAAI;AA8BV,eAAeA,IAAI;AAAC,IAAArB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}