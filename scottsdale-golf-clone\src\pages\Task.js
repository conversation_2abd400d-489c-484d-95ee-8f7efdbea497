import React from 'react';
import styled from 'styled-components';

const TaskContainer = styled.div`
  padding: 20px;
`;

const TaskStatsCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const StatsRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
`;

const StatItem = styled.div`
  text-align: center;
  flex: 1;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #333;
`;

const ProgressCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ProgressTabs = styled.div`
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
`;

const ProgressTab = styled.div`
  flex: 1;
  padding: 12px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: ${props => props.active ? '#8b5a3c' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 16px;
`;

const Task = () => {
  return (
    <TaskContainer>
      <TaskStatsCard>
        <StatsRow>
          <StatItem>
            <StatLabel>All tasks for today</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>Today's remaining tasks</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
        </StatsRow>
      </TaskStatsCard>

      <ProgressCard>
        <ProgressTabs>
          <ProgressTab active>In progress</ProgressTab>
          <ProgressTab>Completed</ProgressTab>
        </ProgressTabs>
        
        <EmptyState>
          No data
        </EmptyState>
      </ProgressCard>
    </TaskContainer>
  );
};

export default Task;
