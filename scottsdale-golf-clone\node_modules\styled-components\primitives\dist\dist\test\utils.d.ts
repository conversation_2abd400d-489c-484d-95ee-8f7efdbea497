/// <reference types="react" />
export declare const seedNextClassnames: (names: string[]) => string[];
export declare const resetStyled: (isServer?: boolean) => (<Target extends import("../types").WebTarget>(tag: Target) => {
    <Props = unknown, Statics = unknown>(initialStyles: import("../types").Styles<Props>, ...interpolations: import("../types").Interpolation<Props>[]): import("../types").IStyledComponent<Target, Props> & Statics;
    attrs(attrs: import("../types").Attrs<unknown>): any;
    withConfig(config: import("../types").StyledOptions<unknown>): any;
}) & {
    a: import("../constructors/constructWithOptions").WebStyled<"a", import("react").DetailedHTMLProps<import("react").AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, unknown>;
    abbr: import("../constructors/constructWithOptions").WebStyled<"abbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    address: import("../constructors/constructWithOptions").WebStyled<"address", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    area: import("../constructors/constructWithOptions").WebStyled<"area", import("react").DetailedHTMLProps<import("react").AreaHTMLAttributes<HTMLAreaElement>, HTMLAreaElement>, unknown>;
    article: import("../constructors/constructWithOptions").WebStyled<"article", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    aside: import("../constructors/constructWithOptions").WebStyled<"aside", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    audio: import("../constructors/constructWithOptions").WebStyled<"audio", import("react").DetailedHTMLProps<import("react").AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>, unknown>;
    b: import("../constructors/constructWithOptions").WebStyled<"b", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    base: import("../constructors/constructWithOptions").WebStyled<"base", import("react").DetailedHTMLProps<import("react").BaseHTMLAttributes<HTMLBaseElement>, HTMLBaseElement>, unknown>;
    bdi: import("../constructors/constructWithOptions").WebStyled<"bdi", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    bdo: import("../constructors/constructWithOptions").WebStyled<"bdo", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    big: import("../constructors/constructWithOptions").WebStyled<"big", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    blockquote: import("../constructors/constructWithOptions").WebStyled<"blockquote", import("react").DetailedHTMLProps<import("react").BlockquoteHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    body: import("../constructors/constructWithOptions").WebStyled<"body", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBodyElement>, HTMLBodyElement>, unknown>;
    br: import("../constructors/constructWithOptions").WebStyled<"br", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLBRElement>, HTMLBRElement>, unknown>;
    button: import("../constructors/constructWithOptions").WebStyled<"button", import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, unknown>;
    canvas: import("../constructors/constructWithOptions").WebStyled<"canvas", import("react").DetailedHTMLProps<import("react").CanvasHTMLAttributes<HTMLCanvasElement>, HTMLCanvasElement>, unknown>;
    caption: import("../constructors/constructWithOptions").WebStyled<"caption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    cite: import("../constructors/constructWithOptions").WebStyled<"cite", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    code: import("../constructors/constructWithOptions").WebStyled<"code", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    col: import("../constructors/constructWithOptions").WebStyled<"col", import("react").DetailedHTMLProps<import("react").ColHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, unknown>;
    colgroup: import("../constructors/constructWithOptions").WebStyled<"colgroup", import("react").DetailedHTMLProps<import("react").ColgroupHTMLAttributes<HTMLTableColElement>, HTMLTableColElement>, unknown>;
    data: import("../constructors/constructWithOptions").WebStyled<"data", import("react").DetailedHTMLProps<import("react").DataHTMLAttributes<HTMLDataElement>, HTMLDataElement>, unknown>;
    datalist: import("../constructors/constructWithOptions").WebStyled<"datalist", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDataListElement>, HTMLDataListElement>, unknown>;
    dd: import("../constructors/constructWithOptions").WebStyled<"dd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    del: import("../constructors/constructWithOptions").WebStyled<"del", import("react").DetailedHTMLProps<import("react").DelHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    details: import("../constructors/constructWithOptions").WebStyled<"details", import("react").DetailedHTMLProps<import("react").DetailsHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    dfn: import("../constructors/constructWithOptions").WebStyled<"dfn", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    dialog: import("../constructors/constructWithOptions").WebStyled<"dialog", import("react").DetailedHTMLProps<import("react").DialogHTMLAttributes<HTMLDialogElement>, HTMLDialogElement>, unknown>;
    div: import("../constructors/constructWithOptions").WebStyled<"div", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, unknown>;
    dl: import("../constructors/constructWithOptions").WebStyled<"dl", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDListElement>, HTMLDListElement>, unknown>;
    dt: import("../constructors/constructWithOptions").WebStyled<"dt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    em: import("../constructors/constructWithOptions").WebStyled<"em", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    embed: import("../constructors/constructWithOptions").WebStyled<"embed", import("react").DetailedHTMLProps<import("react").EmbedHTMLAttributes<HTMLEmbedElement>, HTMLEmbedElement>, unknown>;
    fieldset: import("../constructors/constructWithOptions").WebStyled<"fieldset", import("react").DetailedHTMLProps<import("react").FieldsetHTMLAttributes<HTMLFieldSetElement>, HTMLFieldSetElement>, unknown>;
    figcaption: import("../constructors/constructWithOptions").WebStyled<"figcaption", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    figure: import("../constructors/constructWithOptions").WebStyled<"figure", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    footer: import("../constructors/constructWithOptions").WebStyled<"footer", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    form: import("../constructors/constructWithOptions").WebStyled<"form", import("react").DetailedHTMLProps<import("react").FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, unknown>;
    h1: import("../constructors/constructWithOptions").WebStyled<"h1", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    h2: import("../constructors/constructWithOptions").WebStyled<"h2", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    h3: import("../constructors/constructWithOptions").WebStyled<"h3", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    h4: import("../constructors/constructWithOptions").WebStyled<"h4", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    h5: import("../constructors/constructWithOptions").WebStyled<"h5", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    h6: import("../constructors/constructWithOptions").WebStyled<"h6", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, unknown>;
    head: import("../constructors/constructWithOptions").WebStyled<"head", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHeadElement>, HTMLHeadElement>, unknown>;
    header: import("../constructors/constructWithOptions").WebStyled<"header", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    hgroup: import("../constructors/constructWithOptions").WebStyled<"hgroup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    hr: import("../constructors/constructWithOptions").WebStyled<"hr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLHRElement>, HTMLHRElement>, unknown>;
    html: import("../constructors/constructWithOptions").WebStyled<"html", import("react").DetailedHTMLProps<import("react").HtmlHTMLAttributes<HTMLHtmlElement>, HTMLHtmlElement>, unknown>;
    i: import("../constructors/constructWithOptions").WebStyled<"i", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    iframe: import("../constructors/constructWithOptions").WebStyled<"iframe", import("react").DetailedHTMLProps<import("react").IframeHTMLAttributes<HTMLIFrameElement>, HTMLIFrameElement>, unknown>;
    img: import("../constructors/constructWithOptions").WebStyled<"img", import("react").DetailedHTMLProps<import("react").ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, unknown>;
    input: import("../constructors/constructWithOptions").WebStyled<"input", import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, unknown>;
    ins: import("../constructors/constructWithOptions").WebStyled<"ins", import("react").DetailedHTMLProps<import("react").InsHTMLAttributes<HTMLModElement>, HTMLModElement>, unknown>;
    kbd: import("../constructors/constructWithOptions").WebStyled<"kbd", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    keygen: import("../constructors/constructWithOptions").WebStyled<"keygen", import("react").DetailedHTMLProps<import("react").KeygenHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    label: import("../constructors/constructWithOptions").WebStyled<"label", import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, unknown>;
    legend: import("../constructors/constructWithOptions").WebStyled<"legend", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLLegendElement>, HTMLLegendElement>, unknown>;
    li: import("../constructors/constructWithOptions").WebStyled<"li", import("react").DetailedHTMLProps<import("react").LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, unknown>;
    link: import("../constructors/constructWithOptions").WebStyled<"link", import("react").DetailedHTMLProps<import("react").LinkHTMLAttributes<HTMLLinkElement>, HTMLLinkElement>, unknown>;
    main: import("../constructors/constructWithOptions").WebStyled<"main", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    map: import("../constructors/constructWithOptions").WebStyled<"map", import("react").DetailedHTMLProps<import("react").MapHTMLAttributes<HTMLMapElement>, HTMLMapElement>, unknown>;
    mark: import("../constructors/constructWithOptions").WebStyled<"mark", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    menu: import("../constructors/constructWithOptions").WebStyled<"menu", import("react").DetailedHTMLProps<import("react").MenuHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    menuitem: import("../constructors/constructWithOptions").WebStyled<"menuitem", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    meta: import("../constructors/constructWithOptions").WebStyled<"meta", import("react").DetailedHTMLProps<import("react").MetaHTMLAttributes<HTMLMetaElement>, HTMLMetaElement>, unknown>;
    meter: import("../constructors/constructWithOptions").WebStyled<"meter", import("react").DetailedHTMLProps<import("react").MeterHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    nav: import("../constructors/constructWithOptions").WebStyled<"nav", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    noindex: import("../constructors/constructWithOptions").WebStyled<"noindex", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    noscript: import("../constructors/constructWithOptions").WebStyled<"noscript", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    object: import("../constructors/constructWithOptions").WebStyled<"object", import("react").DetailedHTMLProps<import("react").ObjectHTMLAttributes<HTMLObjectElement>, HTMLObjectElement>, unknown>;
    ol: import("../constructors/constructWithOptions").WebStyled<"ol", import("react").DetailedHTMLProps<import("react").OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, unknown>;
    optgroup: import("../constructors/constructWithOptions").WebStyled<"optgroup", import("react").DetailedHTMLProps<import("react").OptgroupHTMLAttributes<HTMLOptGroupElement>, HTMLOptGroupElement>, unknown>;
    option: import("../constructors/constructWithOptions").WebStyled<"option", import("react").DetailedHTMLProps<import("react").OptionHTMLAttributes<HTMLOptionElement>, HTMLOptionElement>, unknown>;
    output: import("../constructors/constructWithOptions").WebStyled<"output", import("react").DetailedHTMLProps<import("react").OutputHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    p: import("../constructors/constructWithOptions").WebStyled<"p", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, unknown>;
    param: import("../constructors/constructWithOptions").WebStyled<"param", import("react").DetailedHTMLProps<import("react").ParamHTMLAttributes<HTMLParamElement>, HTMLParamElement>, unknown>;
    picture: import("../constructors/constructWithOptions").WebStyled<"picture", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    pre: import("../constructors/constructWithOptions").WebStyled<"pre", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLPreElement>, HTMLPreElement>, unknown>;
    progress: import("../constructors/constructWithOptions").WebStyled<"progress", import("react").DetailedHTMLProps<import("react").ProgressHTMLAttributes<HTMLProgressElement>, HTMLProgressElement>, unknown>;
    q: import("../constructors/constructWithOptions").WebStyled<"q", import("react").DetailedHTMLProps<import("react").QuoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, unknown>;
    rp: import("../constructors/constructWithOptions").WebStyled<"rp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    rt: import("../constructors/constructWithOptions").WebStyled<"rt", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    ruby: import("../constructors/constructWithOptions").WebStyled<"ruby", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    s: import("../constructors/constructWithOptions").WebStyled<"s", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    samp: import("../constructors/constructWithOptions").WebStyled<"samp", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    slot: import("../constructors/constructWithOptions").WebStyled<"slot", import("react").DetailedHTMLProps<import("react").SlotHTMLAttributes<HTMLSlotElement>, HTMLSlotElement>, unknown>;
    script: import("../constructors/constructWithOptions").WebStyled<"script", import("react").DetailedHTMLProps<import("react").ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement>, unknown>;
    section: import("../constructors/constructWithOptions").WebStyled<"section", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    select: import("../constructors/constructWithOptions").WebStyled<"select", import("react").DetailedHTMLProps<import("react").SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>, unknown>;
    small: import("../constructors/constructWithOptions").WebStyled<"small", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    source: import("../constructors/constructWithOptions").WebStyled<"source", import("react").DetailedHTMLProps<import("react").SourceHTMLAttributes<HTMLSourceElement>, HTMLSourceElement>, unknown>;
    span: import("../constructors/constructWithOptions").WebStyled<"span", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, unknown>;
    strong: import("../constructors/constructWithOptions").WebStyled<"strong", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    style: import("../constructors/constructWithOptions").WebStyled<"style", import("react").DetailedHTMLProps<import("react").StyleHTMLAttributes<HTMLStyleElement>, HTMLStyleElement>, unknown>;
    sub: import("../constructors/constructWithOptions").WebStyled<"sub", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    summary: import("../constructors/constructWithOptions").WebStyled<"summary", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    sup: import("../constructors/constructWithOptions").WebStyled<"sup", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    table: import("../constructors/constructWithOptions").WebStyled<"table", import("react").DetailedHTMLProps<import("react").TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, unknown>;
    template: import("../constructors/constructWithOptions").WebStyled<"template", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTemplateElement>, HTMLTemplateElement>, unknown>;
    tbody: import("../constructors/constructWithOptions").WebStyled<"tbody", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, unknown>;
    td: import("../constructors/constructWithOptions").WebStyled<"td", import("react").DetailedHTMLProps<import("react").TdHTMLAttributes<HTMLTableDataCellElement>, HTMLTableDataCellElement>, unknown>;
    textarea: import("../constructors/constructWithOptions").WebStyled<"textarea", import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, unknown>;
    tfoot: import("../constructors/constructWithOptions").WebStyled<"tfoot", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, unknown>;
    th: import("../constructors/constructWithOptions").WebStyled<"th", import("react").DetailedHTMLProps<import("react").ThHTMLAttributes<HTMLTableHeaderCellElement>, HTMLTableHeaderCellElement>, unknown>;
    thead: import("../constructors/constructWithOptions").WebStyled<"thead", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableSectionElement>, HTMLTableSectionElement>, unknown>;
    time: import("../constructors/constructWithOptions").WebStyled<"time", import("react").DetailedHTMLProps<import("react").TimeHTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    title: import("../constructors/constructWithOptions").WebStyled<"title", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTitleElement>, HTMLTitleElement>, unknown>;
    tr: import("../constructors/constructWithOptions").WebStyled<"tr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>, unknown>;
    track: import("../constructors/constructWithOptions").WebStyled<"track", import("react").DetailedHTMLProps<import("react").TrackHTMLAttributes<HTMLTrackElement>, HTMLTrackElement>, unknown>;
    u: import("../constructors/constructWithOptions").WebStyled<"u", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    ul: import("../constructors/constructWithOptions").WebStyled<"ul", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLUListElement>, HTMLUListElement>, unknown>;
    var: import("../constructors/constructWithOptions").WebStyled<"var", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    video: import("../constructors/constructWithOptions").WebStyled<"video", import("react").DetailedHTMLProps<import("react").VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, unknown>;
    wbr: import("../constructors/constructWithOptions").WebStyled<"wbr", import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLElement>, HTMLElement>, unknown>;
    webview: import("../constructors/constructWithOptions").WebStyled<"webview", import("react").DetailedHTMLProps<import("react").WebViewHTMLAttributes<HTMLWebViewElement>, HTMLWebViewElement>, unknown>;
    svg: import("../constructors/constructWithOptions").WebStyled<"svg", import("react").SVGProps<SVGSVGElement>, unknown>;
    animate: import("../constructors/constructWithOptions").WebStyled<"animate", import("react").SVGProps<SVGElement>, unknown>;
    animateMotion: import("../constructors/constructWithOptions").WebStyled<"animateMotion", import("react").SVGProps<SVGElement>, unknown>;
    animateTransform: import("../constructors/constructWithOptions").WebStyled<"animateTransform", import("react").SVGProps<SVGElement>, unknown>;
    circle: import("../constructors/constructWithOptions").WebStyled<"circle", import("react").SVGProps<SVGCircleElement>, unknown>;
    clipPath: import("../constructors/constructWithOptions").WebStyled<"clipPath", import("react").SVGProps<SVGClipPathElement>, unknown>;
    defs: import("../constructors/constructWithOptions").WebStyled<"defs", import("react").SVGProps<SVGDefsElement>, unknown>;
    desc: import("../constructors/constructWithOptions").WebStyled<"desc", import("react").SVGProps<SVGDescElement>, unknown>;
    ellipse: import("../constructors/constructWithOptions").WebStyled<"ellipse", import("react").SVGProps<SVGEllipseElement>, unknown>;
    feBlend: import("../constructors/constructWithOptions").WebStyled<"feBlend", import("react").SVGProps<SVGFEBlendElement>, unknown>;
    feColorMatrix: import("../constructors/constructWithOptions").WebStyled<"feColorMatrix", import("react").SVGProps<SVGFEColorMatrixElement>, unknown>;
    feComponentTransfer: import("../constructors/constructWithOptions").WebStyled<"feComponentTransfer", import("react").SVGProps<SVGFEComponentTransferElement>, unknown>;
    feComposite: import("../constructors/constructWithOptions").WebStyled<"feComposite", import("react").SVGProps<SVGFECompositeElement>, unknown>;
    feConvolveMatrix: import("../constructors/constructWithOptions").WebStyled<"feConvolveMatrix", import("react").SVGProps<SVGFEConvolveMatrixElement>, unknown>;
    feDiffuseLighting: import("../constructors/constructWithOptions").WebStyled<"feDiffuseLighting", import("react").SVGProps<SVGFEDiffuseLightingElement>, unknown>;
    feDisplacementMap: import("../constructors/constructWithOptions").WebStyled<"feDisplacementMap", import("react").SVGProps<SVGFEDisplacementMapElement>, unknown>;
    feDistantLight: import("../constructors/constructWithOptions").WebStyled<"feDistantLight", import("react").SVGProps<SVGFEDistantLightElement>, unknown>;
    feDropShadow: import("../constructors/constructWithOptions").WebStyled<"feDropShadow", import("react").SVGProps<SVGFEDropShadowElement>, unknown>;
    feFlood: import("../constructors/constructWithOptions").WebStyled<"feFlood", import("react").SVGProps<SVGFEFloodElement>, unknown>;
    feFuncA: import("../constructors/constructWithOptions").WebStyled<"feFuncA", import("react").SVGProps<SVGFEFuncAElement>, unknown>;
    feFuncB: import("../constructors/constructWithOptions").WebStyled<"feFuncB", import("react").SVGProps<SVGFEFuncBElement>, unknown>;
    feFuncG: import("../constructors/constructWithOptions").WebStyled<"feFuncG", import("react").SVGProps<SVGFEFuncGElement>, unknown>;
    feFuncR: import("../constructors/constructWithOptions").WebStyled<"feFuncR", import("react").SVGProps<SVGFEFuncRElement>, unknown>;
    feGaussianBlur: import("../constructors/constructWithOptions").WebStyled<"feGaussianBlur", import("react").SVGProps<SVGFEGaussianBlurElement>, unknown>;
    feImage: import("../constructors/constructWithOptions").WebStyled<"feImage", import("react").SVGProps<SVGFEImageElement>, unknown>;
    feMerge: import("../constructors/constructWithOptions").WebStyled<"feMerge", import("react").SVGProps<SVGFEMergeElement>, unknown>;
    feMergeNode: import("../constructors/constructWithOptions").WebStyled<"feMergeNode", import("react").SVGProps<SVGFEMergeNodeElement>, unknown>;
    feMorphology: import("../constructors/constructWithOptions").WebStyled<"feMorphology", import("react").SVGProps<SVGFEMorphologyElement>, unknown>;
    feOffset: import("../constructors/constructWithOptions").WebStyled<"feOffset", import("react").SVGProps<SVGFEOffsetElement>, unknown>;
    fePointLight: import("../constructors/constructWithOptions").WebStyled<"fePointLight", import("react").SVGProps<SVGFEPointLightElement>, unknown>;
    feSpecularLighting: import("../constructors/constructWithOptions").WebStyled<"feSpecularLighting", import("react").SVGProps<SVGFESpecularLightingElement>, unknown>;
    feSpotLight: import("../constructors/constructWithOptions").WebStyled<"feSpotLight", import("react").SVGProps<SVGFESpotLightElement>, unknown>;
    feTile: import("../constructors/constructWithOptions").WebStyled<"feTile", import("react").SVGProps<SVGFETileElement>, unknown>;
    feTurbulence: import("../constructors/constructWithOptions").WebStyled<"feTurbulence", import("react").SVGProps<SVGFETurbulenceElement>, unknown>;
    filter: import("../constructors/constructWithOptions").WebStyled<"filter", import("react").SVGProps<SVGFilterElement>, unknown>;
    foreignObject: import("../constructors/constructWithOptions").WebStyled<"foreignObject", import("react").SVGProps<SVGForeignObjectElement>, unknown>;
    g: import("../constructors/constructWithOptions").WebStyled<"g", import("react").SVGProps<SVGGElement>, unknown>;
    image: import("../constructors/constructWithOptions").WebStyled<"image", import("react").SVGProps<SVGImageElement>, unknown>;
    line: import("../constructors/constructWithOptions").WebStyled<"line", import("react").SVGProps<SVGLineElement>, unknown>;
    linearGradient: import("../constructors/constructWithOptions").WebStyled<"linearGradient", import("react").SVGProps<SVGLinearGradientElement>, unknown>;
    marker: import("../constructors/constructWithOptions").WebStyled<"marker", import("react").SVGProps<SVGMarkerElement>, unknown>;
    mask: import("../constructors/constructWithOptions").WebStyled<"mask", import("react").SVGProps<SVGMaskElement>, unknown>;
    metadata: import("../constructors/constructWithOptions").WebStyled<"metadata", import("react").SVGProps<SVGMetadataElement>, unknown>;
    mpath: import("../constructors/constructWithOptions").WebStyled<"mpath", import("react").SVGProps<SVGElement>, unknown>;
    path: import("../constructors/constructWithOptions").WebStyled<"path", import("react").SVGProps<SVGPathElement>, unknown>;
    pattern: import("../constructors/constructWithOptions").WebStyled<"pattern", import("react").SVGProps<SVGPatternElement>, unknown>;
    polygon: import("../constructors/constructWithOptions").WebStyled<"polygon", import("react").SVGProps<SVGPolygonElement>, unknown>;
    polyline: import("../constructors/constructWithOptions").WebStyled<"polyline", import("react").SVGProps<SVGPolylineElement>, unknown>;
    radialGradient: import("../constructors/constructWithOptions").WebStyled<"radialGradient", import("react").SVGProps<SVGRadialGradientElement>, unknown>;
    rect: import("../constructors/constructWithOptions").WebStyled<"rect", import("react").SVGProps<SVGRectElement>, unknown>;
    stop: import("../constructors/constructWithOptions").WebStyled<"stop", import("react").SVGProps<SVGStopElement>, unknown>;
    switch: import("../constructors/constructWithOptions").WebStyled<"switch", import("react").SVGProps<SVGSwitchElement>, unknown>;
    symbol: import("../constructors/constructWithOptions").WebStyled<"symbol", import("react").SVGProps<SVGSymbolElement>, unknown>;
    text: import("../constructors/constructWithOptions").WebStyled<"text", import("react").SVGProps<SVGTextElement>, unknown>;
    textPath: import("../constructors/constructWithOptions").WebStyled<"textPath", import("react").SVGProps<SVGTextPathElement>, unknown>;
    tspan: import("../constructors/constructWithOptions").WebStyled<"tspan", import("react").SVGProps<SVGTSpanElement>, unknown>;
    use: import("../constructors/constructWithOptions").WebStyled<"use", import("react").SVGProps<SVGUseElement>, unknown>;
    view: import("../constructors/constructWithOptions").WebStyled<"view", import("react").SVGProps<SVGViewElement>, unknown>;
};
export declare const rehydrateTestStyles: () => void;
export declare const stripComments: (str: string) => string;
export declare const stripWhitespace: (str: string) => string;
export declare const getCSS: (scope: Document | HTMLElement) => string;
export declare const expectCSSMatches: (_expectation: string, opts?: {
    ignoreWhitespace: boolean;
}) => string;
export declare const getRenderedCSS: () => string;
//# sourceMappingURL=utils.d.ts.map