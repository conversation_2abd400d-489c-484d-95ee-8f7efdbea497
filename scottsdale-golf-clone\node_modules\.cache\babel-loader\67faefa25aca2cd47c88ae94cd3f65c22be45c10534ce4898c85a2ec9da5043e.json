{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\components\\\\BottomNavigation.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavContainer = styled.nav`\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  border-top: 1px solid #e0e0e0;\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 0;\n  z-index: 100;\n`;\n_c = NavContainer;\nconst NavItem = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  cursor: pointer;\n  padding: 5px;\n  color: ${props => props.active ? '#ff6b35' : '#999'};\n  transition: color 0.3s ease;\n`;\n_c2 = NavItem;\nconst NavIcon = styled.div`\n  font-size: 24px;\n  margin-bottom: 4px;\n`;\n_c3 = NavIcon;\nconst NavLabel = styled.span`\n  font-size: 12px;\n  font-weight: 500;\n`;\n_c4 = NavLabel;\nconst BottomNavigation = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const navItems = [{\n    path: '/',\n    icon: '🏠',\n    label: 'Home'\n  }, {\n    path: '/task',\n    icon: '📋',\n    label: 'Task'\n  }, {\n    path: '/team',\n    icon: '👥',\n    label: 'Team'\n  }, {\n    path: '/vip',\n    icon: '👑',\n    label: 'VIP'\n  }, {\n    path: '/profile',\n    icon: '👤',\n    label: 'Me'\n  }];\n  return /*#__PURE__*/_jsxDEV(NavContainer, {\n    children: navItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n      active: location.pathname === item.path,\n      onClick: () => navigate(item.path),\n      children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(NavLabel, {\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)]\n    }, item.path, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(BottomNavigation, \"VDZHUspDq9N5O9RWjniBrjgIdAA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c5 = BottomNavigation;\nexport default BottomNavigation;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"NavContainer\");\n$RefreshReg$(_c2, \"NavItem\");\n$RefreshReg$(_c3, \"NavIcon\");\n$RefreshReg$(_c4, \"NavLabel\");\n$RefreshReg$(_c5, \"BottomNavigation\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "styled", "jsxDEV", "_jsxDEV", "NavContainer", "nav", "_c", "NavItem", "div", "props", "active", "_c2", "NavIcon", "_c3", "NavLabel", "span", "_c4", "BottomNavigation", "_s", "navigate", "location", "navItems", "path", "icon", "label", "children", "map", "item", "pathname", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/components/BottomNavigation.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst NavContainer = styled.nav`\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #ffffff;\n  border-top: 1px solid #e0e0e0;\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 0;\n  z-index: 100;\n`;\n\nconst NavItem = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  cursor: pointer;\n  padding: 5px;\n  color: ${props => props.active ? '#ff6b35' : '#999'};\n  transition: color 0.3s ease;\n`;\n\nconst NavIcon = styled.div`\n  font-size: 24px;\n  margin-bottom: 4px;\n`;\n\nconst NavLabel = styled.span`\n  font-size: 12px;\n  font-weight: 500;\n`;\n\nconst BottomNavigation = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const navItems = [\n    { path: '/', icon: '🏠', label: 'Home' },\n    { path: '/task', icon: '📋', label: 'Task' },\n    { path: '/team', icon: '👥', label: 'Team' },\n    { path: '/vip', icon: '👑', label: 'VIP' },\n    { path: '/profile', icon: '👤', label: 'Me' },\n  ];\n\n  return (\n    <NavContainer>\n      {navItems.map((item) => (\n        <NavItem\n          key={item.path}\n          active={location.pathname === item.path}\n          onClick={() => navigate(item.path)}\n        >\n          <NavIcon>{item.icon}</NavIcon>\n          <NavLabel>{item.label}</NavLabel>\n        </NavItem>\n      ))}\n    </NavContainer>\n  );\n};\n\nexport default BottomNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGH,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAXIF,YAAY;AAalB,MAAMG,OAAO,GAAGN,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,MAAM;AACrD;AACA,CAAC;AAACC,GAAA,GARIJ,OAAO;AAUb,MAAMK,OAAO,GAAGX,MAAM,CAACO,GAAG;AAC1B;AACA;AACA,CAAC;AAACK,GAAA,GAHID,OAAO;AAKb,MAAME,QAAQ,GAAGb,MAAM,CAACc,IAAI;AAC5B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,QAAQ;AAKd,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAMqB,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAO,CAAC,EACxC;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5C;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5C;IAAEF,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,CAC9C;EAED,oBACErB,OAAA,CAACC,YAAY;IAAAqB,QAAA,EACVJ,QAAQ,CAACK,GAAG,CAAEC,IAAI,iBACjBxB,OAAA,CAACI,OAAO;MAENG,MAAM,EAAEU,QAAQ,CAACQ,QAAQ,KAAKD,IAAI,CAACL,IAAK;MACxCO,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAACQ,IAAI,CAACL,IAAI,CAAE;MAAAG,QAAA,gBAEnCtB,OAAA,CAACS,OAAO;QAAAa,QAAA,EAAEE,IAAI,CAACJ;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAC9B9B,OAAA,CAACW,QAAQ;QAAAW,QAAA,EAAEE,IAAI,CAACH;MAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA,GAL5BN,IAAI,CAACL,IAAI;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMP,CACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAEnB,CAAC;AAACf,EAAA,CA1BID,gBAAgB;EAAA,QACHlB,WAAW,EACXC,WAAW;AAAA;AAAAkC,GAAA,GAFxBjB,gBAAgB;AA4BtB,eAAeA,gBAAgB;AAAC,IAAAX,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAkB,GAAA;AAAAC,YAAA,CAAA7B,EAAA;AAAA6B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}