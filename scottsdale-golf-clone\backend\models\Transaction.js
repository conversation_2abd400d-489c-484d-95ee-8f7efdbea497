const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['recharge', 'withdrawal', 'purchase', 'earning', 'referral', 'bonus'],
    required: true
  },
  category: {
    type: String,
    enum: ['deposit', 'withdraw', 'task', 'product', 'referral', 'bonus', 'commission'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'USDT',
    enum: ['USDT', 'USD', 'BTC', 'ETH']
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  description: {
    type: String,
    required: true
  },
  reference: {
    type: String,
    unique: true,
    required: true
  },
  relatedTo: {
    model: {
      type: String,
      enum: ['Product', 'Task', 'User']
    },
    id: mongoose.Schema.Types.ObjectId
  },
  paymentMethod: {
    type: {
      type: String,
      enum: ['wallet', 'bank', 'crypto', 'internal']
    },
    details: {
      walletAddress: String,
      bankAccount: String,
      cryptoTxHash: String,
      network: String
    }
  },
  fees: {
    amount: {
      type: Number,
      default: 0
    },
    percentage: {
      type: Number,
      default: 0
    },
    description: String
  },
  balanceAfter: {
    total: Number,
    recharge: Number,
    earnings: Number
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    location: String,
    deviceInfo: String
  },
  processing: {
    startedAt: Date,
    completedAt: Date,
    processingTime: Number, // in milliseconds
    attempts: {
      type: Number,
      default: 0
    },
    lastAttempt: Date,
    errorMessage: String
  },
  approval: {
    required: {
      type: Boolean,
      default: false
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    rejectedAt: Date,
    rejectionReason: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for net amount (amount - fees)
transactionSchema.virtual('netAmount').get(function() {
  return this.amount - this.fees.amount;
});

// Virtual for processing duration
transactionSchema.virtual('processingDuration').get(function() {
  if (this.processing.startedAt && this.processing.completedAt) {
    return this.processing.completedAt.getTime() - this.processing.startedAt.getTime();
  }
  return null;
});

// Pre-save middleware to generate reference
transactionSchema.pre('save', function(next) {
  if (!this.reference) {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.reference = `TXN${timestamp}${random}`;
  }
  next();
});

// Method to start processing
transactionSchema.methods.startProcessing = function() {
  this.status = 'processing';
  this.processing.startedAt = new Date();
  this.processing.attempts += 1;
  this.processing.lastAttempt = new Date();
  return this.save();
};

// Method to complete transaction
transactionSchema.methods.complete = function(balanceAfter = null) {
  this.status = 'completed';
  this.processing.completedAt = new Date();
  this.processing.processingTime = this.processingDuration;
  
  if (balanceAfter) {
    this.balanceAfter = balanceAfter;
  }
  
  return this.save();
};

// Method to fail transaction
transactionSchema.methods.fail = function(errorMessage) {
  this.status = 'failed';
  this.processing.errorMessage = errorMessage;
  this.processing.completedAt = new Date();
  return this.save();
};

// Method to cancel transaction
transactionSchema.methods.cancel = function(reason) {
  this.status = 'cancelled';
  this.processing.errorMessage = reason;
  this.processing.completedAt = new Date();
  return this.save();
};

// Method to approve transaction
transactionSchema.methods.approve = function(approvedBy) {
  this.approval.approvedBy = approvedBy;
  this.approval.approvedAt = new Date();
  this.status = 'processing';
  return this.save();
};

// Method to reject transaction
transactionSchema.methods.reject = function(reason, rejectedBy) {
  this.approval.rejectedAt = new Date();
  this.approval.rejectionReason = reason;
  this.status = 'cancelled';
  return this.save();
};

// Static method to create recharge transaction
transactionSchema.statics.createRecharge = function(userId, amount, paymentMethod) {
  return new this({
    user: userId,
    type: 'recharge',
    category: 'deposit',
    amount: amount,
    description: `Account recharge of ${amount} USDT`,
    paymentMethod: paymentMethod,
    approval: {
      required: amount > 1000 // Require approval for large amounts
    }
  });
};

// Static method to create withdrawal transaction
transactionSchema.statics.createWithdrawal = function(userId, amount, paymentMethod) {
  return new this({
    user: userId,
    type: 'withdrawal',
    category: 'withdraw',
    amount: amount,
    description: `Withdrawal of ${amount} USDT`,
    paymentMethod: paymentMethod,
    approval: {
      required: true // All withdrawals require approval
    }
  });
};

// Static method to create earning transaction
transactionSchema.statics.createEarning = function(userId, amount, source, relatedTo = null) {
  return new this({
    user: userId,
    type: 'earning',
    category: source,
    amount: amount,
    description: `Earning from ${source}: ${amount} USDT`,
    relatedTo: relatedTo,
    status: 'completed' // Earnings are automatically completed
  });
};

// Static method to create purchase transaction
transactionSchema.statics.createPurchase = function(userId, amount, productId) {
  return new this({
    user: userId,
    type: 'purchase',
    category: 'product',
    amount: amount,
    description: `Product purchase: ${amount} USDT`,
    relatedTo: {
      model: 'Product',
      id: productId
    },
    status: 'completed' // Purchases are automatically completed
  });
};

// Index for better performance
transactionSchema.index({ user: 1, createdAt: -1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ reference: 1 });
transactionSchema.index({ 'relatedTo.model': 1, 'relatedTo.id': 1 });

module.exports = mongoose.model('Transaction', transactionSchema);
