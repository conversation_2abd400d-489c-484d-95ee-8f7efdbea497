import React from 'react';
import styled from 'styled-components';

const TeamContainer = styled.div`
  padding: 20px;
`;

const InvitationCard = styled.div`
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;

const InvitationTitle = styled.div`
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
`;

const InvitationCode = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
`;

const InvitationLink = styled.div`
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
  word-break: break-all;
`;

const CopyButton = styled.button`
  background-color: #333;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 10px;
`;

const SocialIcons = styled.div`
  display: flex;
  gap: 15px;
  margin-top: 15px;
`;

const SocialIcon = styled.div`
  font-size: 20px;
  cursor: pointer;
`;

const StatsCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;

const StatsRow = styled.div`
  display: flex;
  justify-content: space-between;
  text-align: center;
`;

const StatItem = styled.div`
  flex: 1;
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
`;

const StatValue = styled.div`
  font-size: 20px;
  font-weight: bold;
  color: #333;
`;

const LevelCards = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
`;

const LevelCard = styled.div`
  flex: 1;
  background: ${props => props.gradient};
  border-radius: 12px;
  padding: 20px;
  color: white;
  text-align: center;
`;

const LevelTitle = styled.div`
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
`;

const LevelStats = styled.div`
  font-size: 12px;
  margin-bottom: 5px;
`;

const LevelValue = styled.div`
  font-size: 18px;
  font-weight: bold;
`;

const DetailsButton = styled.button`
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 10px;
  width: 100%;
`;

const Team = () => {
  return (
    <TeamContainer>
      <InvitationCard>
        <InvitationTitle>Invitation code:</InvitationTitle>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InvitationCode>607651</InvitationCode>
          <CopyButton>Copy</CopyButton>
        </div>
        <InvitationTitle>Share your referral link and start earning</InvitationTitle>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InvitationLink>https://scottsdalegolf.cc/#/register?ref=607651</InvitationLink>
          <CopyButton>Copy</CopyButton>
        </div>
        <SocialIcons>
          <SocialIcon>❌</SocialIcon>
          <SocialIcon>📘</SocialIcon>
          <SocialIcon>📧</SocialIcon>
          <SocialIcon>💼</SocialIcon>
          <SocialIcon>💬</SocialIcon>
          <SocialIcon>📷</SocialIcon>
          <SocialIcon>🎵</SocialIcon>
          <SocialIcon>📋</SocialIcon>
        </SocialIcons>
      </InvitationCard>

      <StatsCard>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>
          <span style={{ marginRight: '10px' }}>🇺🇸</span>
          <span>Selection period</span>
        </div>
        <StatsRow>
          <StatItem>
            <StatLabel>Team size</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>Team recharge</StatLabel>
            <StatValue>$0.00</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>Team Withdrawal</StatLabel>
            <StatValue>$0.00</StatValue>
          </StatItem>
        </StatsRow>
        <StatsRow style={{ marginTop: '15px' }}>
          <StatItem>
            <StatLabel>New team</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>First time recharge</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>First withdrawal</StatLabel>
            <StatValue>0</StatValue>
          </StatItem>
        </StatsRow>
      </StatsCard>

      <LevelCards>
        <LevelCard gradient="linear-gradient(135deg, #00cec9, #55a3ff)">
          <LevelTitle>🎯 LEV 1</LevelTitle>
          <LevelStats>Register/Valid</LevelStats>
          <LevelValue>0/0</LevelValue>
          <LevelStats>Total Income</LevelStats>
          <LevelValue>0</LevelValue>
          <DetailsButton>Details</DetailsButton>
        </LevelCard>
        
        <LevelCard gradient="linear-gradient(135deg, #a29bfe, #6c5ce7)">
          <LevelTitle>🎯 LEV 2</LevelTitle>
          <LevelStats>Register/Valid</LevelStats>
          <LevelValue>0/0</LevelValue>
          <LevelStats>Total Income</LevelStats>
          <LevelValue>0</LevelValue>
          <DetailsButton>Details</DetailsButton>
        </LevelCard>
        
        <LevelCard gradient="linear-gradient(135deg, #fd79a8, #e84393)">
          <LevelTitle>🏆 LEV 3</LevelTitle>
          <LevelStats>Register/Valid</LevelStats>
          <LevelValue>0/0</LevelValue>
          <LevelStats>Total Income</LevelStats>
          <LevelValue>0</LevelValue>
          <DetailsButton>Details</DetailsButton>
        </LevelCard>
      </LevelCards>
    </TeamContainer>
  );
};

export default Team;
