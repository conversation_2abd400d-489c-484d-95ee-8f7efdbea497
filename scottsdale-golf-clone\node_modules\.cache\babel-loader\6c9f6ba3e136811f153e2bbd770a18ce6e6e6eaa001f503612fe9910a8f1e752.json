{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\services\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from './api';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER: 'LOAD_USER',\n  UPDATE_USER: 'UPDATE_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...initialState,\n        isLoading: false\n      };\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      const userData = localStorage.getItem('user');\n      if (token && userData) {\n        try {\n          // Verify token is still valid\n          const response = await authAPI.getMe();\n          dispatch({\n            type: AUTH_ACTIONS.LOAD_USER,\n            payload: response.user\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({\n            type: AUTH_ACTIONS.SET_LOADING,\n            payload: false\n          });\n        }\n      } else {\n        dispatch({\n          type: AUTH_ACTIONS.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await authAPI.login(credentials);\n\n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response\n      });\n      return {\n        success: true,\n        user: response.user\n      };\n    } catch (error) {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: error.message || 'Login failed'\n      });\n      return {\n        success: false,\n        error: error.message || 'Login failed'\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await authAPI.register(userData);\n\n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response\n      });\n      return {\n        success: true,\n        user: response.user\n      };\n    } catch (error) {\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: error.message || 'Registration failed'\n      });\n      return {\n        success: false,\n        error: error.message || 'Registration failed'\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear storage and state\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Update user function\n  const updateUser = userData => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData\n    });\n\n    // Update localStorage\n    const updatedUser = {\n      ...state.user,\n      ...userData\n    };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateUser,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "REGISTER_START", "REGISTER_SUCCESS", "REGISTER_FAILURE", "LOGOUT", "LOAD_USER", "UPDATE_USER", "CLEAR_ERROR", "SET_LOADING", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "loadUser", "localStorage", "getItem", "userData", "response", "getMe", "removeItem", "login", "credentials", "setItem", "JSON", "stringify", "success", "message", "register", "logout", "console", "updateUser", "updatedUser", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/services/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from './api';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER: 'LOAD_USER',\n  UPDATE_USER: 'UPDATE_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_LOADING: 'SET_LOADING',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...initialState,\n        isLoading: false,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n      };\n\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      const userData = localStorage.getItem('user');\n\n      if (token && userData) {\n        try {\n          // Verify token is still valid\n          const response = await authAPI.getMe();\n          dispatch({\n            type: AUTH_ACTIONS.LOAD_USER,\n            payload: response.user,\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n        }\n      } else {\n        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n      }\n    };\n\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async (credentials) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n    try {\n      const response = await authAPI.login(credentials);\n      \n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response,\n      });\n      \n      return { success: true, user: response.user };\n    } catch (error) {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: error.message || 'Login failed',\n      });\n      return { success: false, error: error.message || 'Login failed' };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n    try {\n      const response = await authAPI.register(userData);\n      \n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response,\n      });\n      \n      return { success: true, user: response.user };\n    } catch (error) {\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: error.message || 'Registration failed',\n      });\n      return { success: false, error: error.message || 'Registration failed' };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear storage and state\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Update user function\n  const updateUser = (userData) => {\n    dispatch({\n      type: AUTH_ACTIONS.UPDATE_USER,\n      payload: userData,\n    });\n    \n    // Update localStorage\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateUser,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,OAAO;;AAE/B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACI,cAAc;MAC9B,OAAO;QACL,GAAGS,KAAK;QACRf,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACE,aAAa;IAC/B,KAAKF,YAAY,CAACK,gBAAgB;MAChC,OAAO;QACL,GAAGQ,KAAK;QACRlB,IAAI,EAAEmB,MAAM,CAACE,OAAO,CAACrB,IAAI;QACzBC,KAAK,EAAEkB,MAAM,CAACE,OAAO,CAACpB,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACM,gBAAgB;MAChC,OAAO;QACL,GAAGO,KAAK;QACRlB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEe,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKhB,YAAY,CAACO,MAAM;MACtB,OAAO;QACL,GAAGb,YAAY;QACfI,SAAS,EAAE;MACb,CAAC;IAEH,KAAKE,YAAY,CAACQ,SAAS;MACzB,OAAO;QACL,GAAGK,KAAK;QACRlB,IAAI,EAAEmB,MAAM,CAACE,OAAO;QACpBnB,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE;MACb,CAAC;IAEH,KAAKE,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGI,KAAK;QACRlB,IAAI,EAAE;UAAE,GAAGkB,KAAK,CAAClB,IAAI;UAAE,GAAGmB,MAAM,CAACE;QAAQ;MAC3C,CAAC;IAEH,KAAKhB,YAAY,CAACU,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRd,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACW,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRf,SAAS,EAAEgB,MAAM,CAACE;MACpB,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG9B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM+B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAGhC,UAAU,CAACuB,WAAW,EAAElB,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMgC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAM1B,KAAK,GAAG2B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE7C,IAAI5B,KAAK,IAAI6B,QAAQ,EAAE;QACrB,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMnC,OAAO,CAACoC,KAAK,CAAC,CAAC;UACtCN,QAAQ,CAAC;YACPN,IAAI,EAAEf,YAAY,CAACQ,SAAS;YAC5BQ,OAAO,EAAEU,QAAQ,CAAC/B;UACpB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd;UACAwB,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;UAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;UAC/BP,QAAQ,CAAC;YAAEN,IAAI,EAAEf,YAAY,CAACW,WAAW;YAAEK,OAAO,EAAE;UAAM,CAAC,CAAC;QAC9D;MACF,CAAC,MAAM;QACLK,QAAQ,CAAC;UAAEN,IAAI,EAAEf,YAAY,CAACW,WAAW;UAAEK,OAAO,EAAE;QAAM,CAAC,CAAC;MAC9D;IACF,CAAC;IAEDM,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnCT,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACC;IAAY,CAAC,CAAC;IAC5C,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMnC,OAAO,CAACsC,KAAK,CAACC,WAAW,CAAC;;MAEjD;MACAP,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAAC9B,KAAK,CAAC;MAC7C2B,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAAC/B,IAAI,CAAC,CAAC;MAE3D0B,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACE,aAAa;QAChCc,OAAO,EAAEU;MACX,CAAC,CAAC;MAEF,OAAO;QAAEQ,OAAO,EAAE,IAAI;QAAEvC,IAAI,EAAE+B,QAAQ,CAAC/B;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdsB,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACG,aAAa;QAChCa,OAAO,EAAEjB,KAAK,CAACoC,OAAO,IAAI;MAC5B,CAAC,CAAC;MACF,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEnC,KAAK,EAAEA,KAAK,CAACoC,OAAO,IAAI;MAAe,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOX,QAAQ,IAAK;IACnCJ,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACI;IAAe,CAAC,CAAC;IAC/C,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMnC,OAAO,CAAC6C,QAAQ,CAACX,QAAQ,CAAC;;MAEjD;MACAF,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAAC9B,KAAK,CAAC;MAC7C2B,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAAC/B,IAAI,CAAC,CAAC;MAE3D0B,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACK,gBAAgB;QACnCW,OAAO,EAAEU;MACX,CAAC,CAAC;MAEF,OAAO;QAAEQ,OAAO,EAAE,IAAI;QAAEvC,IAAI,EAAE+B,QAAQ,CAAC/B;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdsB,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACM,gBAAgB;QACnCU,OAAO,EAAEjB,KAAK,CAACoC,OAAO,IAAI;MAC5B,CAAC,CAAC;MACF,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEnC,KAAK,EAAEA,KAAK,CAACoC,OAAO,IAAI;MAAsB,CAAC;IAC1E;EACF,CAAC;;EAED;EACA,MAAME,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM9C,OAAO,CAAC8C,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAwB,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;MAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;MAC/BP,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACO;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAId,QAAQ,IAAK;IAC/BJ,QAAQ,CAAC;MACPN,IAAI,EAAEf,YAAY,CAACS,WAAW;MAC9BO,OAAO,EAAES;IACX,CAAC,CAAC;;IAEF;IACA,MAAMe,WAAW,GAAG;MAAE,GAAG3B,KAAK,CAAClB,IAAI;MAAE,GAAG8B;IAAS,CAAC;IAClDF,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACO,WAAW,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBpB,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACU;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMgC,KAAK,GAAG;IACZ,GAAG7B,KAAK;IACRgB,KAAK;IACLO,QAAQ;IACRC,MAAM;IACNE,UAAU;IACVE;EACF,CAAC;EAED,oBACEhD,OAAA,CAACwB,WAAW,CAAC0B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EAChCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA3B,EAAA,CAjIaF,YAAY;AAAA8B,EAAA,GAAZ9B,YAAY;AAkIzB,OAAO,MAAM+B,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG/D,UAAU,CAAC6B,WAAW,CAAC;EACvC,IAAI,CAACkC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAehC,WAAW;AAAC,IAAA+B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}