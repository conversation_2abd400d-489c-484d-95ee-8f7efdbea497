{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\nconst SpinnerContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n`;\n_c = SpinnerContainer;\nconst Spinner = styled.div`\n  width: 50px;\n  height: 50px;\n  border: 4px solid #e0e0e0;\n  border-top: 4px solid #ff6b35;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin-bottom: 20px;\n`;\n_c2 = Spinner;\nconst LoadingText = styled.div`\n  font-size: 16px;\n  color: #666;\n  text-align: center;\n`;\n_c3 = LoadingText;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 30px;\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 24px;\n`;\n_c5 = LogoIcon;\nconst LogoText = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n`;\n_c6 = LogoText;\nconst LoadingSpinner = ({\n  message = 'Loading...'\n}) => {\n  return /*#__PURE__*/_jsxDEV(SpinnerContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Logo, {\n      children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n        children: \"S\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        children: \"scottsdalegolf Mall\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingText, {\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_c7 = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"SpinnerContainer\");\n$RefreshReg$(_c2, \"Spinner\");\n$RefreshReg$(_c3, \"LoadingText\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"LogoText\");\n$RefreshReg$(_c7, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "jsxDEV", "_jsxDEV", "spin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Spinner", "_c2", "LoadingText", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "LogoText", "_c6", "LoadingSpinner", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\n\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\n\nconst SpinnerContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n`;\n\nconst Spinner = styled.div`\n  width: 50px;\n  height: 50px;\n  border: 4px solid #e0e0e0;\n  border-top: 4px solid #ff6b35;\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin-bottom: 20px;\n`;\n\nconst LoadingText = styled.div`\n  font-size: 16px;\n  color: #666;\n  text-align: center;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 30px;\n`;\n\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 24px;\n`;\n\nconst LogoText = styled.div`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n`;\n\nconst LoadingSpinner = ({ message = 'Loading...' }) => {\n  return (\n    <SpinnerContainer>\n      <Logo>\n        <LogoIcon>S</LogoIcon>\n        <LogoText>scottsdalegolf Mall</LogoText>\n      </Logo>\n      <Spinner />\n      <LoadingText>{message}</LoadingText>\n    </SpinnerContainer>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,IAAI,GAAGH,SAAS;AACtB;AACA;AACA,CAAC;AAED,MAAMI,gBAAgB,GAAGL,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,gBAAgB;AAStB,MAAMG,OAAO,GAAGR,MAAM,CAACM,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,eAAeF,IAAI;AACnB;AACA,CAAC;AAACK,GAAA,GARID,OAAO;AAUb,MAAME,WAAW,GAAGV,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,WAAW;AAMjB,MAAME,IAAI,GAAGZ,MAAM,CAACM,GAAG;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GALID,IAAI;AAOV,MAAME,QAAQ,GAAGd,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAXID,QAAQ;AAad,MAAME,QAAQ,GAAGhB,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,QAAQ;AAMd,MAAME,cAAc,GAAGA,CAAC;EAAEC,OAAO,GAAG;AAAa,CAAC,KAAK;EACrD,oBACEhB,OAAA,CAACE,gBAAgB;IAAAe,QAAA,gBACfjB,OAAA,CAACS,IAAI;MAAAQ,QAAA,gBACHjB,OAAA,CAACW,QAAQ;QAAAM,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACtBrB,OAAA,CAACa,QAAQ;QAAAI,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACPrB,OAAA,CAACK,OAAO;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXrB,OAAA,CAACO,WAAW;MAAAU,QAAA,EAAED;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEvB,CAAC;AAACC,GAAA,GAXIP,cAAc;AAapB,eAAeA,cAAc;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAnB,EAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}