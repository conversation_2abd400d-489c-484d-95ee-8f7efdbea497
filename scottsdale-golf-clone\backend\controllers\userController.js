const User = require('../models/User');
const Transaction = require('../models/Transaction');

// @desc    Get user balance
// @route   GET /api/users/balance
// @access  Private
const getBalance = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    res.status(200).json({
      success: true,
      balance: user.balance
    });
  } catch (error) {
    console.error('Get balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting balance'
    });
  }
};

// @desc    Get user referral info
// @route   GET /api/users/referral
// @access  Private
const getReferralInfo = async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('referral.referredUsers.user', 'email profile.firstName profile.lastName vipLevel createdAt');
    
    res.status(200).json({
      success: true,
      referral: {
        code: user.referral.code,
        totalReferrals: user.referral.totalReferrals,
        totalEarnings: user.referral.totalEarnings,
        referredUsers: user.referral.referredUsers,
        referralLink: `${process.env.CLIENT_URL}/#/register?ref=${user.referral.code}`
      }
    });
  } catch (error) {
    console.error('Get referral info error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting referral info'
    });
  }
};

// @desc    Get team statistics
// @route   GET /api/users/team
// @access  Private
const getTeamStats = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get detailed team members
    const teamMembers = await User.find({
      'referral.referredBy': req.user.id
    }).select('email profile vipLevel balance.total createdAt');
    
    // Calculate level-wise statistics
    const levelStats = {
      level1: { count: 0, earnings: 0 },
      level2: { count: 0, earnings: 0 },
      level3: { count: 0, earnings: 0 }
    };
    
    // For now, all direct referrals are level 1
    levelStats.level1.count = teamMembers.length;
    
    res.status(200).json({
      success: true,
      team: {
        ...user.team,
        members: teamMembers,
        levelStats: levelStats
      }
    });
  } catch (error) {
    console.error('Get team stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting team stats'
    });
  }
};

// @desc    Get user transactions
// @route   GET /api/users/transactions
// @access  Private
const getTransactions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const type = req.query.type; // filter by transaction type
    const status = req.query.status; // filter by status
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { user: req.user.id };
    if (type) query.type = type;
    if (status) query.status = status;
    
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('relatedTo.id', 'name');
    
    const total = await Transaction.countDocuments(query);
    
    res.status(200).json({
      success: true,
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get transactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting transactions'
    });
  }
};

// @desc    Update VIP level
// @route   PUT /api/users/vip-level
// @access  Private
const updateVipLevel = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    await user.updateVipLevel();
    
    res.status(200).json({
      success: true,
      message: 'VIP level updated',
      vipLevel: user.vipLevel
    });
  } catch (error) {
    console.error('Update VIP level error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating VIP level'
    });
  }
};

// @desc    Get user statistics
// @route   GET /api/users/stats
// @access  Private
const getUserStats = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get transaction statistics
    const transactionStats = await Transaction.aggregate([
      { $match: { user: user._id } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);
    
    // Get recent activity
    const recentTransactions = await Transaction.find({ user: user._id })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('type amount description createdAt status');
    
    res.status(200).json({
      success: true,
      stats: {
        profile: {
          vipLevel: user.vipLevel,
          memberSince: user.createdAt,
          lastLogin: user.lastLogin,
          loginCount: user.loginCount
        },
        balance: user.balance,
        team: user.team,
        tasks: user.tasks,
        transactions: transactionStats,
        recentActivity: recentTransactions
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting user statistics'
    });
  }
};

// @desc    Get VIP members list (for VIP page)
// @route   GET /api/users/vip-members
// @access  Private
const getVipMembers = async (req, res) => {
  try {
    const vipMembers = await User.find({
      vipLevel: { $gte: 1 },
      status: 'active'
    })
    .select('email vipLevel balance.total')
    .sort({ vipLevel: -1, 'balance.total': -1 })
    .limit(20);
    
    // Mask email for privacy
    const maskedMembers = vipMembers.map(member => ({
      email: member.email.replace(/(.{1})(.*)(@.*)/, '$1***$3'),
      vipLevel: member.vipLevel,
      earnings: `+$${member.balance.total.toFixed(2)}`
    }));
    
    res.status(200).json({
      success: true,
      members: maskedMembers
    });
  } catch (error) {
    console.error('Get VIP members error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting VIP members'
    });
  }
};

module.exports = {
  getBalance,
  getReferralInfo,
  getTeamStats,
  getTransactions,
  updateVipLevel,
  getUserStats,
  getVipMembers
};
