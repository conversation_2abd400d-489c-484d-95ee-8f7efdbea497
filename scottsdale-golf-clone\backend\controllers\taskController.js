const Task = require('../models/Task');
const Product = require('../models/Product');
const User = require('../models/User');
const Transaction = require('../models/Transaction');

// @desc    Get user tasks
// @route   GET /api/tasks
// @access  Private
const getTasks = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const type = req.query.type;
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { user: req.user.id };
    if (status) query.status = status;
    if (type) query.type = type;
    
    const tasks = await Task.find(query)
      .populate('product', 'name icon price vipLevel')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Task.countDocuments(query);
    
    res.status(200).json({
      success: true,
      tasks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting tasks'
    });
  }
};

// @desc    Get daily tasks
// @route   GET /api/tasks/daily
// @access  Private
const getDailyTasks = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Get available products for user's VIP level
    const availableProducts = await Product.find({
      status: 'active',
      vipLevel: { $lte: user.vipLevel }
    }).limit(5);
    
    // Create or get daily tasks
    const dailyTasks = await Task.createDailyTasks(req.user.id, availableProducts);
    
    // Populate product details
    await Task.populate(dailyTasks, {
      path: 'product',
      select: 'name icon price vipLevel earnings.dailyEarning'
    });
    
    res.status(200).json({
      success: true,
      tasks: dailyTasks,
      summary: {
        total: dailyTasks.length,
        completed: dailyTasks.filter(task => task.status === 'completed').length,
        remaining: dailyTasks.filter(task => task.status !== 'completed').length
      }
    });
  } catch (error) {
    console.error('Get daily tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting daily tasks'
    });
  }
};

// @desc    Start a task
// @route   POST /api/tasks/:id/start
// @access  Private
const startTask = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id).populate('product');
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
    
    // Check if task belongs to user
    if (task.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this task'
      });
    }
    
    // Check if task can be started
    if (task.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Task cannot be started'
      });
    }
    
    // Check user requirements
    const user = await User.findById(req.user.id);
    if (user.vipLevel < task.requirements.minVipLevel) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient VIP level'
      });
    }
    
    if (user.balance.total < task.requirements.minBalance) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient balance'
      });
    }
    
    // Start the task
    await task.startTask();
    
    // Emit real-time notification
    if (req.io) {
      req.io.to(`user-${req.user.id}`).emit('task-started', {
        taskId: task._id,
        productName: task.product.name,
        earnings: task.earnings.amount
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Task started successfully',
      task
    });
  } catch (error) {
    console.error('Start task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error starting task'
    });
  }
};

// @desc    Complete a task
// @route   POST /api/tasks/:id/complete
// @access  Private
const completeTask = async (req, res) => {
  try {
    const task = await Task.findById(req.params.id).populate('product');
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
    
    // Check if task belongs to user
    if (task.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this task'
      });
    }
    
    // Check if task can be completed
    if (task.status !== 'in-progress') {
      return res.status(400).json({
        success: false,
        message: 'Task is not in progress'
      });
    }
    
    // Check if task is expired
    if (task.isExpired) {
      await task.failTask('Task expired');
      return res.status(400).json({
        success: false,
        message: 'Task has expired'
      });
    }
    
    const session = await User.startSession();
    session.startTransaction();
    
    try {
      // Complete the task
      await task.completeTask(task.earnings.amount);
      
      // Update user balance and stats
      const user = await User.findById(req.user.id);
      user.balance.total += task.earnings.amount;
      user.balance.earnings += task.earnings.amount;
      user.tasks.completedTasks += 1;
      user.tasks.totalEarnings += task.earnings.amount;
      user.tasks.lastTaskDate = new Date();
      
      await user.save({ session });
      
      // Create earning transaction
      const transaction = Transaction.createEarning(
        user._id,
        task.earnings.amount,
        'task',
        { model: 'Task', id: task._id }
      );
      transaction.balanceAfter = {
        total: user.balance.total,
        recharge: user.balance.recharge,
        earnings: user.balance.earnings
      };
      await transaction.save({ session });
      
      // Update VIP level if needed
      await user.updateVipLevel();
      
      await session.commitTransaction();
      
      // Emit real-time notification
      if (req.io) {
        req.io.to(`user-${req.user.id}`).emit('task-completed', {
          taskId: task._id,
          productName: task.product.name,
          earnings: task.earnings.amount,
          newBalance: user.balance.total,
          newVipLevel: user.vipLevel
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Task completed successfully',
        earnings: task.earnings.amount,
        newBalance: user.balance.total,
        newVipLevel: user.vipLevel
      });
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error) {
    console.error('Complete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error completing task'
    });
  }
};

// @desc    Get task statistics
// @route   GET /api/tasks/stats
// @access  Private
const getTaskStats = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Get today's tasks
    const todayTasks = await Task.find({
      user: req.user.id,
      createdAt: { $gte: today, $lt: tomorrow }
    });
    
    // Get all-time stats
    const allTimeStats = await Task.aggregate([
      { $match: { user: req.user._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalEarnings: { $sum: '$earnings.amount' }
        }
      }
    ]);
    
    const stats = {
      today: {
        total: todayTasks.length,
        completed: todayTasks.filter(task => task.status === 'completed').length,
        remaining: todayTasks.filter(task => task.status !== 'completed').length,
        earnings: todayTasks
          .filter(task => task.status === 'completed')
          .reduce((sum, task) => sum + task.earnings.amount, 0)
      },
      allTime: {
        total: allTimeStats.reduce((sum, stat) => sum + stat.count, 0),
        completed: allTimeStats.find(stat => stat._id === 'completed')?.count || 0,
        totalEarnings: allTimeStats.reduce((sum, stat) => sum + (stat.totalEarnings || 0), 0)
      }
    };
    
    res.status(200).json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting task statistics'
    });
  }
};

module.exports = {
  getTasks,
  getDailyTasks,
  startTask,
  completeTask,
  getTaskStats
};
