const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Product description is required']
  },
  category: {
    type: String,
    required: [true, 'Product category is required'],
    enum: ['golf-clubs', 'golf-balls', 'accessories', 'apparel', 'equipment']
  },
  price: {
    type: Number,
    required: [true, 'Product price is required'],
    min: [0, 'Price cannot be negative']
  },
  vipLevel: {
    type: Number,
    required: [true, 'VIP level is required'],
    min: 0,
    max: 10
  },
  vipPrice: {
    type: Number,
    required: [true, 'VIP price is required'],
    min: [0, 'VIP price cannot be negative']
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  icon: {
    type: String,
    default: '🏌️'
  },
  specifications: {
    brand: String,
    model: String,
    material: String,
    weight: String,
    dimensions: String,
    color: [String]
  },
  inventory: {
    stock: {
      type: Number,
      default: 0,
      min: 0
    },
    reserved: {
      type: Number,
      default: 0,
      min: 0
    },
    sold: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  earnings: {
    dailyEarning: {
      type: Number,
      required: [true, 'Daily earning is required'],
      min: 0
    },
    totalEarning: {
      type: Number,
      default: 0,
      min: 0
    },
    commissionRate: {
      type: Number,
      default: 0.1,
      min: 0,
      max: 1
    }
  },
  requirements: {
    minVipLevel: {
      type: Number,
      default: 0,
      min: 0,
      max: 10
    },
    minBalance: {
      type: Number,
      default: 0,
      min: 0
    },
    maxPurchasePerDay: {
      type: Number,
      default: 1,
      min: 1
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'out-of-stock', 'discontinued'],
    default: 'active'
  },
  isLocked: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  tags: [String],
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    comment: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  purchaseHistory: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    quantity: {
      type: Number,
      default: 1
    },
    price: Number,
    purchaseDate: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for available stock
productSchema.virtual('availableStock').get(function() {
  return this.inventory.stock - this.inventory.reserved;
});

// Virtual for total revenue
productSchema.virtual('totalRevenue').get(function() {
  return this.inventory.sold * this.price;
});

// Method to check if user can purchase
productSchema.methods.canUserPurchase = function(user) {
  // Check VIP level requirement
  if (user.vipLevel < this.requirements.minVipLevel) {
    return { canPurchase: false, reason: 'Insufficient VIP level' };
  }
  
  // Check balance requirement
  if (user.balance.total < this.requirements.minBalance) {
    return { canPurchase: false, reason: 'Insufficient balance' };
  }
  
  // Check if product is active
  if (this.status !== 'active') {
    return { canPurchase: false, reason: 'Product not available' };
  }
  
  // Check stock
  if (this.availableStock <= 0) {
    return { canPurchase: false, reason: 'Out of stock' };
  }
  
  return { canPurchase: true };
};

// Method to add review
productSchema.methods.addReview = function(userId, rating, comment) {
  this.reviews.push({
    user: userId,
    rating: rating,
    comment: comment
  });
  
  // Recalculate average rating
  const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
  this.rating.average = totalRating / this.reviews.length;
  this.rating.count = this.reviews.length;
  
  return this.save();
};

// Method to record purchase
productSchema.methods.recordPurchase = function(userId, quantity = 1, price) {
  this.purchaseHistory.push({
    user: userId,
    quantity: quantity,
    price: price || this.price
  });
  
  this.inventory.sold += quantity;
  this.inventory.stock -= quantity;
  this.earnings.totalEarning += (price || this.price) * quantity;
  
  return this.save();
};

// Index for better performance
productSchema.index({ vipLevel: 1 });
productSchema.index({ category: 1 });
productSchema.index({ status: 1 });
productSchema.index({ price: 1 });
productSchema.index({ 'rating.average': -1 });

module.exports = mongoose.model('Product', productSchema);
