const express = require('express');
const {
  getProducts,
  getProduct,
  purchaseProduct,
  addReview,
  getCategories,
  getFeaturedProducts
} = require('../controllers/productController');
const { protect, requireBalance, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting
const productRateLimit = rateLimit(15 * 60 * 1000, 100); // 100 requests per 15 minutes

// All routes are protected
router.use(protect);
router.use(productRateLimit);

// Product routes
router.get('/', getProducts);
router.get('/categories', getCategories);
router.get('/featured', getFeaturedProducts);
router.get('/:id', getProduct);
router.post('/:id/purchase', purchaseProduct);
router.post('/:id/review', addReview);

module.exports = router;
