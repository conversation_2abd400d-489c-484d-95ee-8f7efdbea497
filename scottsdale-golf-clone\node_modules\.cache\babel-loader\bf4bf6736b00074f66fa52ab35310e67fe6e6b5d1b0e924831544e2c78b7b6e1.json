{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\n// Create a context for wallet integration (Phase 3)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const WalletContext = /*#__PURE__*/React.createContext({\n  connected: false,\n  address: '',\n  balance: 0,\n  connect: () => {},\n  disconnect: () => {}\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsxDEV", "_jsxDEV", "WalletContext", "createContext", "connected", "address", "balance", "connect", "disconnect", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\n// Create a context for wallet integration (Phase 3)\nexport const WalletContext = React.createContext({\n  connected: false,\n  address: '',\n  balance: 0,\n  connect: () => {},\n  disconnect: () => {}\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,aAAa,gBAAGN,KAAK,CAACO,aAAa,CAAC;EAC/CC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;EACjBC,UAAU,EAAEA,CAAA,KAAM,CAAC;AACrB,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGZ,QAAQ,CAACa,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTZ,OAAA,CAACL,KAAK,CAACkB,UAAU;EAAAC,QAAA,eACfd,OAAA,CAACH,GAAG;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA;AACA;AACApB,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}