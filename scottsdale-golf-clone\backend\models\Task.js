const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  type: {
    type: String,
    enum: ['daily', 'bonus', 'referral', 'vip'],
    default: 'daily'
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  earnings: {
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'USDT'
    }
  },
  status: {
    type: String,
    enum: ['pending', 'in-progress', 'completed', 'failed', 'expired'],
    default: 'pending'
  },
  progress: {
    current: {
      type: Number,
      default: 0,
      min: 0
    },
    target: {
      type: Number,
      required: true,
      min: 1
    },
    percentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  timeframe: {
    startTime: {
      type: Date,
      default: Date.now
    },
    endTime: Date,
    duration: {
      type: Number, // in minutes
      default: 1440 // 24 hours
    },
    completedAt: Date
  },
  requirements: {
    minVipLevel: {
      type: Number,
      default: 0
    },
    minBalance: {
      type: Number,
      default: 0
    },
    maxAttempts: {
      type: Number,
      default: 1
    }
  },
  attempts: {
    count: {
      type: Number,
      default: 0
    },
    history: [{
      attemptedAt: {
        type: Date,
        default: Date.now
      },
      result: {
        type: String,
        enum: ['success', 'failed', 'partial']
      },
      earnings: Number,
      notes: String
    }]
  },
  metadata: {
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      default: 'easy'
    },
    category: String,
    tags: [String],
    instructions: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for time remaining
taskSchema.virtual('timeRemaining').get(function() {
  if (!this.timeframe.endTime) {
    const endTime = new Date(this.timeframe.startTime.getTime() + (this.timeframe.duration * 60000));
    return Math.max(0, endTime.getTime() - Date.now());
  }
  return Math.max(0, this.timeframe.endTime.getTime() - Date.now());
});

// Virtual for is expired
taskSchema.virtual('isExpired').get(function() {
  return this.timeRemaining <= 0;
});

// Virtual for can attempt
taskSchema.virtual('canAttempt').get(function() {
  return this.attempts.count < this.requirements.maxAttempts && 
         !this.isExpired && 
         this.status !== 'completed';
});

// Pre-save middleware to calculate progress percentage
taskSchema.pre('save', function(next) {
  if (this.progress.target > 0) {
    this.progress.percentage = Math.min(100, (this.progress.current / this.progress.target) * 100);
  }
  
  // Auto-complete if target reached
  if (this.progress.current >= this.progress.target && this.status !== 'completed') {
    this.status = 'completed';
    this.timeframe.completedAt = new Date();
  }
  
  // Auto-expire if time is up
  if (this.isExpired && this.status === 'in-progress') {
    this.status = 'expired';
  }
  
  next();
});

// Method to start task
taskSchema.methods.startTask = async function() {
  if (this.status !== 'pending') {
    throw new Error('Task cannot be started');
  }
  
  this.status = 'in-progress';
  this.timeframe.startTime = new Date();
  
  if (this.timeframe.duration) {
    this.timeframe.endTime = new Date(Date.now() + (this.timeframe.duration * 60000));
  }
  
  return this.save();
};

// Method to complete task
taskSchema.methods.completeTask = async function(earnings = null) {
  if (this.status !== 'in-progress') {
    throw new Error('Task is not in progress');
  }
  
  this.status = 'completed';
  this.progress.current = this.progress.target;
  this.timeframe.completedAt = new Date();
  
  // Record attempt
  this.attempts.history.push({
    attemptedAt: new Date(),
    result: 'success',
    earnings: earnings || this.earnings.amount,
    notes: 'Task completed successfully'
  });
  
  this.attempts.count += 1;
  
  return this.save();
};

// Method to fail task
taskSchema.methods.failTask = async function(reason = 'Task failed') {
  this.status = 'failed';
  
  // Record attempt
  this.attempts.history.push({
    attemptedAt: new Date(),
    result: 'failed',
    earnings: 0,
    notes: reason
  });
  
  this.attempts.count += 1;
  
  return this.save();
};

// Method to update progress
taskSchema.methods.updateProgress = async function(increment = 1) {
  this.progress.current = Math.min(this.progress.target, this.progress.current + increment);
  return this.save();
};

// Static method to create daily tasks for user
taskSchema.statics.createDailyTasks = async function(userId, products) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Check if daily tasks already exist for today
  const existingTasks = await this.find({
    user: userId,
    type: 'daily',
    createdAt: { $gte: today }
  });
  
  if (existingTasks.length > 0) {
    return existingTasks;
  }
  
  // Create new daily tasks
  const dailyTasks = [];
  for (const product of products) {
    const task = new this({
      user: userId,
      product: product._id,
      type: 'daily',
      title: `Complete ${product.name} Task`,
      description: `Earn ${product.earnings.dailyEarning} USDT by completing this golf equipment task`,
      earnings: {
        amount: product.earnings.dailyEarning,
        currency: 'USDT'
      },
      progress: {
        target: 1
      },
      timeframe: {
        duration: 1440 // 24 hours
      },
      requirements: {
        minVipLevel: product.vipLevel,
        minBalance: product.price
      }
    });
    
    dailyTasks.push(task);
  }
  
  return await this.insertMany(dailyTasks);
};

// Index for better performance
taskSchema.index({ user: 1, createdAt: -1 });
taskSchema.index({ status: 1 });
taskSchema.index({ type: 1 });
taskSchema.index({ 'timeframe.endTime': 1 });

module.exports = mongoose.model('Task', taskSchema);
