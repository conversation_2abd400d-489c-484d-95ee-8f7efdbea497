const Product = require('../models/Product');
const User = require('../models/User');
const Transaction = require('../models/Transaction');

// @desc    Get all products
// @route   GET /api/products
// @access  Private
const getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const category = req.query.category;
    const vipLevel = req.query.vipLevel;
    const status = req.query.status || 'active';
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query = { status };
    if (category) query.category = category;
    if (vipLevel !== undefined) query.vipLevel = { $lte: parseInt(vipLevel) };
    
    const products = await Product.find(query)
      .sort({ vipLevel: 1, price: 1 })
      .skip(skip)
      .limit(limit)
      .select('-reviews -purchaseHistory');
    
    const total = await Product.countDocuments(query);
    
    // Check which products user can access
    const user = await User.findById(req.user.id);
    const productsWithAccess = products.map(product => {
      const canPurchase = product.canUserPurchase(user);
      return {
        ...product.toObject(),
        canPurchase: canPurchase.canPurchase,
        purchaseReason: canPurchase.reason || null,
        isLocked: user.vipLevel < product.vipLevel
      };
    });
    
    res.status(200).json({
      success: true,
      products: productsWithAccess,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting products'
    });
  }
};

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Private
const getProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('reviews.user', 'email profile.firstName profile.lastName');
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Check if user can purchase
    const user = await User.findById(req.user.id);
    const canPurchase = product.canUserPurchase(user);
    
    res.status(200).json({
      success: true,
      product: {
        ...product.toObject(),
        canPurchase: canPurchase.canPurchase,
        purchaseReason: canPurchase.reason || null,
        isLocked: user.vipLevel < product.vipLevel
      }
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting product'
    });
  }
};

// @desc    Purchase product
// @route   POST /api/products/:id/purchase
// @access  Private
const purchaseProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    const user = await User.findById(req.user.id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Check if user can purchase
    const canPurchase = product.canUserPurchase(user);
    if (!canPurchase.canPurchase) {
      return res.status(403).json({
        success: false,
        message: canPurchase.reason
      });
    }
    
    // Check if user has sufficient balance
    if (user.balance.total < product.price) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }
    
    // Process purchase
    const session = await User.startSession();
    session.startTransaction();
    
    try {
      // Deduct balance
      user.balance.total -= product.price;
      await user.save({ session });
      
      // Record purchase in product
      await product.recordPurchase(user._id, 1, product.price);
      
      // Create purchase transaction
      const transaction = Transaction.createPurchase(user._id, product.price, product._id);
      transaction.balanceAfter = {
        total: user.balance.total,
        recharge: user.balance.recharge,
        earnings: user.balance.earnings
      };
      await transaction.save({ session });
      
      // Update VIP level if needed
      await user.updateVipLevel();
      
      await session.commitTransaction();
      
      // Emit real-time notification
      if (req.io) {
        req.io.to(`user-${user._id}`).emit('purchase-success', {
          product: {
            id: product._id,
            name: product.name,
            price: product.price
          },
          newBalance: user.balance.total,
          newVipLevel: user.vipLevel
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Product purchased successfully',
        purchase: {
          product: {
            id: product._id,
            name: product.name,
            price: product.price
          },
          transaction: transaction.reference,
          newBalance: user.balance.total,
          newVipLevel: user.vipLevel
        }
      });
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error) {
    console.error('Purchase product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error processing purchase'
    });
  }
};

// @desc    Add product review
// @route   POST /api/products/:id/review
// @access  Private
const addReview = async (req, res) => {
  try {
    const { rating, comment } = req.body;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating must be between 1 and 5'
      });
    }
    
    const product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Check if user already reviewed this product
    const existingReview = product.reviews.find(
      review => review.user.toString() === req.user.id
    );
    
    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this product'
      });
    }
    
    // Add review
    await product.addReview(req.user.id, rating, comment);
    
    res.status(201).json({
      success: true,
      message: 'Review added successfully'
    });
  } catch (error) {
    console.error('Add review error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error adding review'
    });
  }
};

// @desc    Get product categories
// @route   GET /api/products/categories
// @access  Private
const getCategories = async (req, res) => {
  try {
    const categories = await Product.distinct('category', { status: 'active' });
    
    res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting categories'
    });
  }
};

// @desc    Get featured products
// @route   GET /api/products/featured
// @access  Private
const getFeaturedProducts = async (req, res) => {
  try {
    const products = await Product.find({
      status: 'active',
      isFeatured: true
    })
    .sort({ vipLevel: 1 })
    .limit(6)
    .select('-reviews -purchaseHistory');
    
    // Check access for user
    const user = await User.findById(req.user.id);
    const productsWithAccess = products.map(product => {
      const canPurchase = product.canUserPurchase(user);
      return {
        ...product.toObject(),
        canPurchase: canPurchase.canPurchase,
        isLocked: user.vipLevel < product.vipLevel
      };
    });
    
    res.status(200).json({
      success: true,
      products: productsWithAccess
    });
  } catch (error) {
    console.error('Get featured products error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting featured products'
    });
  }
};

module.exports = {
  getProducts,
  getProduct,
  purchaseProduct,
  addReview,
  getCategories,
  getFeaturedProducts
};
