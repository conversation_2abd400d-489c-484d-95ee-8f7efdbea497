const express = require('express');
const {
  getTeamOverview,
  getReferralStats,
  getTeamMember,
  getTeamPerformance
} = require('../controllers/teamController');
const { protect, rateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply rate limiting
const teamRateLimit = rateLimit(15 * 60 * 1000, 50); // 50 requests per 15 minutes

// All routes are protected
router.use(protect);
router.use(teamRateLimit);

// Team routes
router.get('/overview', getTeamOverview);
router.get('/referral-stats', getReferralStats);
router.get('/performance', getTeamPerformance);
router.get('/member/:id', getTeamMember);

module.exports = router;
