const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Product = require('../models/Product');
const User = require('../models/User');

// Load env vars
dotenv.config();

// Connect to database
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scottsdale-golf');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Sample products data
const products = [
  {
    name: 'Golf clubs',
    description: 'Premium golf clubs for VIP1 members. Start your golf journey with quality equipment.',
    category: 'golf-clubs',
    price: 5.86,
    vipLevel: 1,
    vipPrice: 5.86,
    icon: '🏌️',
    earnings: {
      dailyEarning: 0.59,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 1,
      minBalance: 5.86
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Advanced golf clubs for VIP2 members. Enhanced performance and durability.',
    category: 'golf-clubs',
    price: 26.00,
    vipLevel: 2,
    vipPrice: 26.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 2.60,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 2,
      minBalance: 26.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Professional golf clubs for VIP3 members. Tournament-grade equipment.',
    category: 'golf-clubs',
    price: 59.00,
    vipLevel: 3,
    vipPrice: 59.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 5.90,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 3,
      minBalance: 59.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Elite golf clubs for VIP4 members. Superior craftsmanship and performance.',
    category: 'golf-clubs',
    price: 117.00,
    vipLevel: 4,
    vipPrice: 117.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 11.70,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 4,
      minBalance: 117.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Luxury golf clubs for VIP5 members. Exclusive design and materials.',
    category: 'golf-clubs',
    price: 278.00,
    vipLevel: 5,
    vipPrice: 278.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 27.80,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 5,
      minBalance: 278.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Premium luxury golf clubs for VIP6 members. Handcrafted excellence.',
    category: 'golf-clubs',
    price: 536.00,
    vipLevel: 6,
    vipPrice: 536.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 53.60,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 6,
      minBalance: 536.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Ultra-premium golf clubs for VIP7 members. Championship-level equipment.',
    category: 'golf-clubs',
    price: 1350.00,
    vipLevel: 7,
    vipPrice: 1350.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 135.00,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 7,
      minBalance: 1350.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Exclusive golf clubs for VIP8 members. Limited edition masterpieces.',
    category: 'golf-clubs',
    price: 2350.00,
    vipLevel: 8,
    vipPrice: 2350.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 235.00,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 8,
      minBalance: 2350.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Legendary golf clubs for VIP9 members. Unmatched quality and prestige.',
    category: 'golf-clubs',
    price: 4900.00,
    vipLevel: 9,
    vipPrice: 4900.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 490.00,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 9,
      minBalance: 4900.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  },
  {
    name: 'Golf clubs',
    description: 'Ultimate golf clubs for VIP10 members. The pinnacle of golf equipment.',
    category: 'golf-clubs',
    price: 16500.00,
    vipLevel: 10,
    vipPrice: 16500.00,
    icon: '🏌️',
    earnings: {
      dailyEarning: 1650.00,
      commissionRate: 0.1
    },
    requirements: {
      minVipLevel: 10,
      minBalance: 16500.00
    },
    inventory: {
      stock: 100
    },
    isFeatured: true,
    isLocked: true
  }
];

// Import data
const importData = async () => {
  try {
    await connectDB();
    
    // Clear existing data
    await Product.deleteMany();
    console.log('Products cleared...');
    
    // Insert products
    await Product.insertMany(products);
    console.log('Products imported...');
    
    console.log('Data import completed successfully!');
    process.exit();
  } catch (error) {
    console.error('Error importing data:', error);
    process.exit(1);
  }
};

// Delete data
const deleteData = async () => {
  try {
    await connectDB();
    
    await Product.deleteMany();
    await User.deleteMany();
    
    console.log('Data destroyed successfully!');
    process.exit();
  } catch (error) {
    console.error('Error destroying data:', error);
    process.exit(1);
  }
};

// Check command line arguments
if (process.argv[2] === '-i') {
  importData();
} else if (process.argv[2] === '-d') {
  deleteData();
} else {
  console.log('Usage:');
  console.log('node utils/seeder.js -i  (import data)');
  console.log('node utils/seeder.js -d  (delete data)');
}
