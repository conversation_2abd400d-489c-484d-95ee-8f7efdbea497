import { io } from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
  }

  connect(userId) {
    if (this.socket) {
      this.disconnect();
    }

    this.socket = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
      transports: ['websocket'],
      autoConnect: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.isConnected = true;
      
      // Join user-specific room
      if (userId) {
        this.socket.emit('join-user-room', userId);
      }
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.isConnected = false;
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Listen for real-time events
  onNewReferral(callback) {
    if (this.socket) {
      this.socket.on('new-referral', callback);
    }
  }

  onTaskStarted(callback) {
    if (this.socket) {
      this.socket.on('task-started', callback);
    }
  }

  onTaskCompleted(callback) {
    if (this.socket) {
      this.socket.on('task-completed', callback);
    }
  }

  onPurchaseSuccess(callback) {
    if (this.socket) {
      this.socket.on('purchase-success', callback);
    }
  }

  onRechargeCreated(callback) {
    if (this.socket) {
      this.socket.on('recharge-created', callback);
    }
  }

  onWithdrawalCreated(callback) {
    if (this.socket) {
      this.socket.on('withdrawal-created', callback);
    }
  }

  onTransactionCancelled(callback) {
    if (this.socket) {
      this.socket.on('transaction-cancelled', callback);
    }
  }

  // Remove event listeners
  offNewReferral() {
    if (this.socket) {
      this.socket.off('new-referral');
    }
  }

  offTaskStarted() {
    if (this.socket) {
      this.socket.off('task-started');
    }
  }

  offTaskCompleted() {
    if (this.socket) {
      this.socket.off('task-completed');
    }
  }

  offPurchaseSuccess() {
    if (this.socket) {
      this.socket.off('purchase-success');
    }
  }

  offRechargeCreated() {
    if (this.socket) {
      this.socket.off('recharge-created');
    }
  }

  offWithdrawalCreated() {
    if (this.socket) {
      this.socket.off('withdrawal-created');
    }
  }

  offTransactionCancelled() {
    if (this.socket) {
      this.socket.off('transaction-cancelled');
    }
  }

  // Generic emit function
  emit(event, data) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    }
  }

  // Generic listener function
  on(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // Generic remove listener function
  off(event) {
    if (this.socket) {
      this.socket.off(event);
    }
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
