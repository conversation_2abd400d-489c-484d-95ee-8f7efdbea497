import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../services/AuthContext';
import { productAPI, userAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';

const HomeContainer = styled.div`
  padding: 20px;
`;

const UserInfo = styled.div`
  background-color: #d3d3d3;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const UserEmail = styled.div`
  font-size: 14px;
  color: #333;
`;

const VipBadge = styled.span`
  background-color: #ffd700;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
`;

const BalanceInfo = styled.div`
  font-size: 14px;
  color: #333;
`;

const HeroSection = styled.div`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  color: white;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="3" fill="rgba(255,255,255,0.1)"/></svg>');
`;

const ActionButtons = styled.div`
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
`;

const ActionButton = styled.button`
  background: ${props => props.color || '#007bff'};
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-width: 80px;
`;

const SectionTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0 15px 0;
  color: #333;
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
`;

const ProductCard = styled.div`
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 15px;
  position: relative;
`;

const ProductImage = styled.div`
  width: 100%;
  height: 120px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
`;

const ProductName = styled.h3`
  font-size: 14px;
  font-weight: 500;
  margin: 5px 0;
  color: #333;
`;

const ProductPrice = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 5px 0;
`;

const VipPrice = styled.div`
  font-size: 12px;
  color: #666;
  text-decoration: line-through;
`;

const VipBadgeProduct = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ffd700;
  color: #333;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
`;

const LockIcon = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #666;
`;

const Home = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [balance, setBalance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch products and balance in parallel
        const [productsResponse, balanceResponse] = await Promise.all([
          productAPI.getProducts({ limit: 10 }),
          userAPI.getBalance()
        ]);

        setProducts(productsResponse.products || []);
        setBalance(balanceResponse.balance);
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message || 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProductClick = async (product) => {
    if (product.isLocked) {
      alert(`This product requires VIP${product.vipLevel}. Your current level: VIP${user?.vipLevel || 0}`);
      return;
    }

    if (!product.canPurchase) {
      alert(product.purchaseReason || 'Cannot purchase this product');
      return;
    }

    if (window.confirm(`Purchase ${product.name} for $${product.price}?`)) {
      try {
        await productAPI.purchaseProduct(product._id);
        alert('Product purchased successfully!');

        // Refresh balance
        const balanceResponse = await userAPI.getBalance();
        setBalance(balanceResponse.balance);
      } catch (err) {
        alert(err.message || 'Purchase failed');
      }
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading products..." />;
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h3>Error loading data</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  return (
    <HomeContainer>
      <UserInfo>
        <div>
          <UserEmail>{user?.email || 'Loading...'}</UserEmail>
          <VipBadge>VIP{user?.vipLevel || 0}</VipBadge>
        </div>
        <BalanceInfo>${balance?.total?.toFixed(2) || '0.00'}</BalanceInfo>
      </UserInfo>

      <HeroSection>
        <h2>Task Hall</h2>
      </HeroSection>

      <ActionButtons>
        <ActionButton color="#6c5ce7">
          <span>💰</span>
          <span>Recharge</span>
        </ActionButton>
        <ActionButton color="#a29bfe">
          <span>👤</span>
          <span>VIP Line</span>
        </ActionButton>
        <ActionButton color="#fd79a8">
          <span>📱</span>
          <span>App</span>
        </ActionButton>
        <ActionButton color="#00b894">
          <span>👥</span>
          <span>Company Profile</span>
        </ActionButton>
      </ActionButtons>

      <SectionTitle>Task Hall</SectionTitle>
      <ProductGrid>
        {products.map((product) => (
          <ProductCard
            key={product._id}
            onClick={() => handleProductClick(product)}
            style={{ cursor: 'pointer' }}
          >
            <VipBadgeProduct>VIP{product.vipLevel}</VipBadgeProduct>
            <ProductImage>
              {product.icon}
              {product.isLocked && <LockIcon>🔒</LockIcon>}
            </ProductImage>
            <ProductName>{product.name}</ProductName>
            <ProductPrice>${product.price}</ProductPrice>
            <VipPrice>Daily: ${product.earnings?.dailyEarning || 0}</VipPrice>
          </ProductCard>
        ))}
      </ProductGrid>
    </HomeContainer>
  );
};

export default Home;
