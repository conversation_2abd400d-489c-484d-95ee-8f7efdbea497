import React from 'react';
import styled from 'styled-components';

const HomeContainer = styled.div`
  padding: 20px;
`;

const UserInfo = styled.div`
  background-color: #d3d3d3;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const UserEmail = styled.div`
  font-size: 14px;
  color: #333;
`;

const VipBadge = styled.span`
  background-color: #ffd700;
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
`;

const BalanceInfo = styled.div`
  font-size: 14px;
  color: #333;
`;

const HeroSection = styled.div`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  color: white;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="3" fill="rgba(255,255,255,0.1)"/></svg>');
`;

const ActionButtons = styled.div`
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
`;

const ActionButton = styled.button`
  background: ${props => props.color || '#007bff'};
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-width: 80px;
`;

const SectionTitle = styled.h2`
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0 15px 0;
  color: #333;
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
`;

const ProductCard = styled.div`
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 15px;
  position: relative;
`;

const ProductImage = styled.div`
  width: 100%;
  height: 120px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
`;

const ProductName = styled.h3`
  font-size: 14px;
  font-weight: 500;
  margin: 5px 0;
  color: #333;
`;

const ProductPrice = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 5px 0;
`;

const VipPrice = styled.div`
  font-size: 12px;
  color: #666;
  text-decoration: line-through;
`;

const VipBadgeProduct = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ffd700;
  color: #333;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
`;

const LockIcon = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #666;
`;

const Home = () => {
  const products = [
    { id: 1, name: 'Golf clubs', price: '$5.86', vipPrice: 'VIP1', icon: '🏌️', locked: true },
    { id: 2, name: 'Golf clubs', price: '$26.00', vipPrice: 'VIP2', icon: '🏌️', locked: true },
    { id: 3, name: 'Golf clubs', price: '$59.00', vipPrice: 'VIP3', icon: '🏌️', locked: true },
    { id: 4, name: 'Golf clubs', price: '$117.00', vipPrice: 'VIP4', icon: '🏌️', locked: true },
    { id: 5, name: 'Golf clubs', price: '$278.00', vipPrice: 'VIP5', icon: '🏌️', locked: true },
    { id: 6, name: 'Golf clubs', price: '$536.00', vipPrice: 'VIP6', icon: '🏌️', locked: true },
    { id: 7, name: 'Golf clubs', price: '$1,350.00', vipPrice: 'VIP7', icon: '🏌️', locked: true },
    { id: 8, name: 'Golf clubs', price: '$2,350.00', vipPrice: 'VIP8', icon: '🏌️', locked: true },
    { id: 9, name: 'Golf clubs', price: '$4,900.00', vipPrice: 'VIP9', icon: '🏌️', locked: true },
    { id: 10, name: 'Golf clubs', price: '$16,500.00', vipPrice: 'VIP10', icon: '🏌️', locked: true },
  ];

  return (
    <HomeContainer>
      <UserInfo>
        <div>
          <UserEmail><EMAIL></UserEmail>
          <VipBadge>VIP0</VipBadge>
        </div>
        <BalanceInfo>$0</BalanceInfo>
      </UserInfo>

      <HeroSection>
        <h2>Task Hall</h2>
      </HeroSection>

      <ActionButtons>
        <ActionButton color="#6c5ce7">
          <span>💰</span>
          <span>Recharge</span>
        </ActionButton>
        <ActionButton color="#a29bfe">
          <span>👤</span>
          <span>VIP Line</span>
        </ActionButton>
        <ActionButton color="#fd79a8">
          <span>📱</span>
          <span>App</span>
        </ActionButton>
        <ActionButton color="#00b894">
          <span>👥</span>
          <span>Company Profile</span>
        </ActionButton>
      </ActionButtons>

      <SectionTitle>Task Hall</SectionTitle>
      <ProductGrid>
        {products.map((product) => (
          <ProductCard key={product.id}>
            <VipBadgeProduct>{product.vipPrice}</VipBadgeProduct>
            <ProductImage>
              {product.icon}
              {product.locked && <LockIcon>🔒</LockIcon>}
            </ProductImage>
            <ProductName>{product.name}</ProductName>
            <ProductPrice>{product.price}</ProductPrice>
            <VipPrice>{product.vipPrice}</VipPrice>
          </ProductCard>
        ))}
      </ProductGrid>
    </HomeContainer>
  );
};

export default Home;
