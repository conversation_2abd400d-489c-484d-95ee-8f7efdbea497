import { NativeStyled } from '../constructors/constructWithOptions';
import css from '../constructors/css';
import withTheme from '../hoc/withTheme';
import useTheme from '../hooks/useTheme';
import ThemeProvider, { ThemeConsumer, ThemeContext } from '../models/ThemeProvider';
import { NativeTarget } from '../types';
import isStyledComponent from '../utils/isStyledComponent';
declare const styled: (<Target extends NativeTarget>(tag: Target) => {
    <Props = unknown, Statics = unknown>(initialStyles: import("../types").Styles<Props>, ...interpolations: import("../types").Interpolation<Props>[]): import("../types").IStyledNativeComponent<Target, Props> & Statics;
    attrs(attrs: import("../types").Attrs<unknown>): any;
    withConfig(config: import("../types").StyledNativeOptions<unknown>): any;
}) & {
    ActivityIndicator: NativeStyled<typeof import("react-native").ActivityIndicator, import("react-native").ActivityIndicatorProps, unknown>;
    ActivityIndicatorIOS: NativeStyled<unknown, {}, unknown>;
    ART: NativeStyled<unknown, {}, unknown>;
    Button: NativeStyled<typeof import("react-native").Button, import("react-native").ButtonProps, unknown>;
    DatePickerIOS: NativeStyled<typeof import("react-native").DatePickerIOS, import("react-native").DatePickerIOSProps, unknown>;
    DrawerLayoutAndroid: NativeStyled<typeof import("react-native").DrawerLayoutAndroid, import("react-native").DrawerLayoutAndroidProps, unknown>;
    FlatList: NativeStyled<typeof import("react-native").FlatList, import("react-native").FlatListProps<unknown>, unknown>;
    Image: NativeStyled<typeof import("react-native").Image, import("react-native").ImageProps, unknown>;
    ImageBackground: NativeStyled<typeof import("react-native").ImageBackground, import("react-native").ImageBackgroundProps, unknown>;
    ImageEditor: NativeStyled<never, never, unknown>;
    ImageStore: NativeStyled<never, never, unknown>;
    KeyboardAvoidingView: NativeStyled<typeof import("react-native").KeyboardAvoidingView, import("react-native").KeyboardAvoidingViewProps, unknown>;
    ListView: NativeStyled<typeof import("react-native").ListView, import("react-native").ListViewProps, unknown>;
    MapView: NativeStyled<unknown, {}, unknown>;
    Modal: NativeStyled<typeof import("react-native").Modal, import("react-native").ModalProps, unknown>;
    NavigatorIOS: NativeStyled<typeof import("react-native").NavigatorIOS, import("react-native").NavigatorIOSProps, unknown>;
    Picker: NativeStyled<typeof import("react-native").Picker, import("react-native").PickerProps, unknown>;
    PickerIOS: NativeStyled<typeof import("react-native").PickerIOS, import("react-native").PickerIOSProps, unknown>;
    Pressable: NativeStyled<import("react").ForwardRefExoticComponent<import("react-native").PressableProps & import("react").RefAttributes<import("react-native").View>>, import("react-native").PressableProps & import("react").RefAttributes<import("react-native").View>, unknown>;
    ProgressBarAndroid: NativeStyled<typeof import("react-native").ProgressBarAndroid, import("react-native").ProgressBarAndroidProps, unknown>;
    ProgressViewIOS: NativeStyled<typeof import("react-native").ProgressViewIOS, import("react-native").ProgressViewIOSProps, unknown>;
    RecyclerViewBackedScrollView: NativeStyled<typeof import("react-native").RecyclerViewBackedScrollView, import("react-native").RecyclerViewBackedScrollViewProps, unknown>;
    RefreshControl: NativeStyled<typeof import("react-native").RefreshControl, import("react-native").RefreshControlProps, unknown>;
    SafeAreaView: NativeStyled<typeof import("react-native").SafeAreaView, import("react-native").ViewProps, unknown>;
    ScrollView: NativeStyled<typeof import("react-native").ScrollView, import("react-native").ScrollViewProps, unknown>;
    SectionList: NativeStyled<typeof import("react-native").SectionList, import("react-native").SectionListProps<unknown, unknown>, unknown>;
    SegmentedControlIOS: NativeStyled<typeof import("react-native").SegmentedControlIOS, import("react-native").SegmentedControlIOSProps, unknown>;
    Slider: NativeStyled<typeof import("react-native").Slider, import("react-native").SliderProps, unknown>;
    SliderIOS: NativeStyled<unknown, {}, unknown>;
    SnapshotViewIOS: NativeStyled<typeof import("react-native").SnapshotViewIOS, import("react-native").SnapshotViewIOSProps, unknown>;
    StatusBar: NativeStyled<typeof import("react-native").StatusBar, import("react-native").StatusBarProps, unknown>;
    SwipeableListView: NativeStyled<typeof import("react-native").SwipeableListView, import("react-native").SwipeableListViewProps, unknown>;
    Switch: NativeStyled<typeof import("react-native").Switch, import("react-native").SwitchProps, unknown>;
    SwitchAndroid: NativeStyled<unknown, {}, unknown>;
    SwitchIOS: NativeStyled<typeof import("react-native").SwitchIOS, import("react-native").SwitchIOSProps, unknown>;
    TabBarIOS: NativeStyled<typeof import("react-native").TabBarIOS, import("react-native").TabBarIOSProps, unknown>;
    Text: NativeStyled<typeof import("react-native").Text, import("react-native").TextProps, unknown>;
    TextInput: NativeStyled<typeof import("react-native").TextInput, import("react-native").TextInputProps, unknown>;
    ToastAndroid: NativeStyled<never, never, unknown>;
    ToolbarAndroid: NativeStyled<typeof import("react-native").ToolbarAndroid, import("react-native").ToolbarAndroidProps, unknown>;
    Touchable: NativeStyled<never, never, unknown>;
    TouchableHighlight: NativeStyled<typeof import("react-native").TouchableHighlight, import("react-native").TouchableHighlightProps, unknown>;
    TouchableNativeFeedback: NativeStyled<typeof import("react-native").TouchableNativeFeedback, import("react-native").TouchableNativeFeedbackProps, unknown>;
    TouchableOpacity: NativeStyled<typeof import("react-native").TouchableOpacity, import("react-native").TouchableOpacityProps, unknown>;
    TouchableWithoutFeedback: NativeStyled<typeof import("react-native").TouchableWithoutFeedback, import("react-native").TouchableWithoutFeedbackProps, unknown>;
    View: NativeStyled<typeof import("react-native").View, import("react-native").ViewProps, unknown>;
    ViewPagerAndroid: NativeStyled<typeof import("react-native").ViewPagerAndroid, import("react-native").ViewPagerAndroidProps, unknown>;
    VirtualizedList: NativeStyled<typeof import("react-native").VirtualizedList, import("react-native").VirtualizedListProps<unknown>, unknown>;
    WebView: NativeStyled<unknown, {}, unknown>;
};
export { css, isStyledComponent, ThemeProvider, ThemeConsumer, ThemeContext, withTheme, useTheme };
export default styled;
//# sourceMappingURL=index.d.ts.map