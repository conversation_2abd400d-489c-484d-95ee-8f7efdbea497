{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hello\\\\scottsdale-golf-clone\\\\src\\\\pages\\\\Profile.js\";\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\nimport { userAPI, transactionAPI } from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileContainer = styled.div`\n  padding: 20px;\n`;\n_c = ProfileContainer;\nconst BalanceCard = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  padding: 20px;\n  color: white;\n  margin-bottom: 20px;\n`;\n_c2 = BalanceCard;\nconst UserEmail = styled.div`\n  font-size: 16px;\n  margin-bottom: 10px;\n`;\n_c3 = UserEmail;\nconst VipBadge = styled.span`\n  background-color: #ffd700;\n  color: #333;\n  padding: 4px 12px;\n  border-radius: 15px;\n  font-size: 12px;\n  font-weight: bold;\n`;\n_c4 = VipBadge;\nconst BalanceRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin: 15px 0;\n`;\n_c5 = BalanceRow;\nconst BalanceLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n`;\n_c6 = BalanceLabel;\nconst BalanceAmount = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n`;\n_c7 = BalanceAmount;\nconst MenuList = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  overflow: hidden;\n  margin-bottom: 20px;\n`;\n_c8 = MenuList;\nconst MenuItem = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #f8f9fa;\n  }\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c9 = MenuItem;\nconst MenuIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: ${props => props.bgColor || '#e9ecef'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n  font-size: 18px;\n`;\n_c0 = MenuIcon;\nconst MenuText = styled.div`\n  flex: 1;\n  font-size: 16px;\n  color: #333;\n`;\n_c1 = MenuText;\nconst MenuArrow = styled.div`\n  font-size: 18px;\n  color: #999;\n`;\n_c10 = MenuArrow;\nconst SignOutButton = styled.button`\n  width: 100%;\n  background-color: #6c757d;\n  color: white;\n  border: none;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 20px;\n\n  &:hover {\n    background-color: #5a6268;\n  }\n`;\n_c11 = SignOutButton;\nconst Profile = () => {\n  const menuItems = [{\n    icon: '💰',\n    text: 'Recharge',\n    bgColor: '#e3f2fd'\n  }, {\n    icon: '💸',\n    text: 'Withdraw',\n    bgColor: '#f3e5f5'\n  }, {\n    icon: '👤',\n    text: 'Account',\n    bgColor: '#e8f5e8'\n  }, {\n    icon: '📊',\n    text: 'Financial records',\n    bgColor: '#fff3e0'\n  }, {\n    icon: '🔒',\n    text: 'Change Password',\n    bgColor: '#fce4ec'\n  }];\n  return /*#__PURE__*/_jsxDEV(ProfileContainer, {\n    children: [/*#__PURE__*/_jsxDEV(BalanceCard, {\n      children: [/*#__PURE__*/_jsxDEV(UserEmail, {\n        children: [\"<EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(VipBadge, {\n          children: \"VIP0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BalanceRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(BalanceLabel, {\n            children: \"Total balance (USDT)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BalanceAmount, {\n            children: \"0.19\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right'\n          },\n          children: [/*#__PURE__*/_jsxDEV(BalanceLabel, {\n            children: \"Recharge amount (USDT)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BalanceAmount, {\n            children: \"0.00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuList, {\n      children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n        children: [/*#__PURE__*/_jsxDEV(MenuIcon, {\n          bgColor: item.bgColor,\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuText, {\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuArrow, {\n          children: \"\\u203A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SignOutButton, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDEAA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Sign out\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_c12 = Profile;\nexport default Profile;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"ProfileContainer\");\n$RefreshReg$(_c2, \"BalanceCard\");\n$RefreshReg$(_c3, \"UserEmail\");\n$RefreshReg$(_c4, \"VipBadge\");\n$RefreshReg$(_c5, \"BalanceRow\");\n$RefreshReg$(_c6, \"BalanceLabel\");\n$RefreshReg$(_c7, \"BalanceAmount\");\n$RefreshReg$(_c8, \"MenuList\");\n$RefreshReg$(_c9, \"MenuItem\");\n$RefreshReg$(_c0, \"MenuIcon\");\n$RefreshReg$(_c1, \"MenuText\");\n$RefreshReg$(_c10, \"MenuArrow\");\n$RefreshReg$(_c11, \"SignOutButton\");\n$RefreshReg$(_c12, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useAuth", "userAPI", "transactionAPI", "LoadingSpinner", "jsxDEV", "_jsxDEV", "ProfileContainer", "div", "_c", "BalanceCard", "_c2", "UserEmail", "_c3", "VipBadge", "span", "_c4", "BalanceRow", "_c5", "BalanceLabel", "_c6", "BalanceAmount", "_c7", "MenuList", "_c8", "MenuItem", "_c9", "MenuIcon", "props", "bgColor", "_c0", "MenuText", "_c1", "MenuArrow", "_c10", "SignOutButton", "button", "_c11", "Profile", "menuItems", "icon", "text", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "map", "item", "index", "_c12", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Hello/scottsdale-golf-clone/src/pages/Profile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../services/AuthContext';\nimport { userAPI, transactionAPI } from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst ProfileContainer = styled.div`\n  padding: 20px;\n`;\n\nconst BalanceCard = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  padding: 20px;\n  color: white;\n  margin-bottom: 20px;\n`;\n\nconst UserEmail = styled.div`\n  font-size: 16px;\n  margin-bottom: 10px;\n`;\n\nconst VipBadge = styled.span`\n  background-color: #ffd700;\n  color: #333;\n  padding: 4px 12px;\n  border-radius: 15px;\n  font-size: 12px;\n  font-weight: bold;\n`;\n\nconst BalanceRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin: 15px 0;\n`;\n\nconst BalanceLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n`;\n\nconst BalanceAmount = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n`;\n\nconst MenuList = styled.div`\n  background-color: white;\n  border-radius: 12px;\n  overflow: hidden;\n  margin-bottom: 20px;\n`;\n\nconst MenuItem = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #f8f9fa;\n  }\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst MenuIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: ${props => props.bgColor || '#e9ecef'};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 15px;\n  font-size: 18px;\n`;\n\nconst MenuText = styled.div`\n  flex: 1;\n  font-size: 16px;\n  color: #333;\n`;\n\nconst MenuArrow = styled.div`\n  font-size: 18px;\n  color: #999;\n`;\n\nconst SignOutButton = styled.button`\n  width: 100%;\n  background-color: #6c757d;\n  color: white;\n  border: none;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 20px;\n\n  &:hover {\n    background-color: #5a6268;\n  }\n`;\n\nconst Profile = () => {\n  const menuItems = [\n    { icon: '💰', text: 'Recharge', bgColor: '#e3f2fd' },\n    { icon: '💸', text: 'Withdraw', bgColor: '#f3e5f5' },\n    { icon: '👤', text: 'Account', bgColor: '#e8f5e8' },\n    { icon: '📊', text: 'Financial records', bgColor: '#fff3e0' },\n    { icon: '🔒', text: 'Change Password', bgColor: '#fce4ec' },\n  ];\n\n  return (\n    <ProfileContainer>\n      <BalanceCard>\n        <UserEmail>\n          <EMAIL>\n          <br />\n          <VipBadge>VIP0</VipBadge>\n        </UserEmail>\n        \n        <BalanceRow>\n          <div>\n            <BalanceLabel>Total balance (USDT)</BalanceLabel>\n            <BalanceAmount>0.19</BalanceAmount>\n          </div>\n          <div style={{ textAlign: 'right' }}>\n            <BalanceLabel>Recharge amount (USDT)</BalanceLabel>\n            <BalanceAmount>0.00</BalanceAmount>\n          </div>\n        </BalanceRow>\n      </BalanceCard>\n\n      <MenuList>\n        {menuItems.map((item, index) => (\n          <MenuItem key={index}>\n            <MenuIcon bgColor={item.bgColor}>\n              {item.icon}\n            </MenuIcon>\n            <MenuText>{item.text}</MenuText>\n            <MenuArrow>›</MenuArrow>\n          </MenuItem>\n        ))}\n      </MenuList>\n\n      <SignOutButton>\n        <span>🚪</span>\n        <span>Sign out</span>\n      </SignOutButton>\n    </ProfileContainer>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,EAAEC,cAAc,QAAQ,iBAAiB;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,gBAAgB,GAAGP,MAAM,CAACQ,GAAG;AACnC;AACA,CAAC;AAACC,EAAA,GAFIF,gBAAgB;AAItB,MAAMG,WAAW,GAAGV,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,WAAW;AAQjB,MAAME,SAAS,GAAGZ,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA,CAAC;AAACK,GAAA,GAHID,SAAS;AAKf,MAAME,QAAQ,GAAGd,MAAM,CAACe,IAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,QAAQ;AASd,MAAMG,UAAU,GAAGjB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAJID,UAAU;AAMhB,MAAME,YAAY,GAAGnB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA,CAAC;AAACY,GAAA,GAHID,YAAY;AAKlB,MAAME,aAAa,GAAGrB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA,CAAC;AAACc,GAAA,GAHID,aAAa;AAKnB,MAAME,QAAQ,GAAGvB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,QAAQ;AAOd,MAAME,QAAQ,GAAGzB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAfID,QAAQ;AAiBd,MAAME,QAAQ,GAAG3B,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,sBAAsBoB,KAAK,IAAIA,KAAK,CAACC,OAAO,IAAI,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIH,QAAQ;AAYd,MAAMI,QAAQ,GAAG/B,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAJID,QAAQ;AAMd,MAAME,SAAS,GAAGjC,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA,CAAC;AAAC0B,IAAA,GAHID,SAAS;AAKf,MAAME,aAAa,GAAGnC,MAAM,CAACoC,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAnBIF,aAAa;AAqBnB,MAAMG,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,UAAU;IAAEZ,OAAO,EAAE;EAAU,CAAC,EACpD;IAAEW,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,UAAU;IAAEZ,OAAO,EAAE;EAAU,CAAC,EACpD;IAAEW,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEZ,OAAO,EAAE;EAAU,CAAC,EACnD;IAAEW,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,mBAAmB;IAAEZ,OAAO,EAAE;EAAU,CAAC,EAC7D;IAAEW,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,iBAAiB;IAAEZ,OAAO,EAAE;EAAU,CAAC,CAC5D;EAED,oBACEvB,OAAA,CAACC,gBAAgB;IAAAmC,QAAA,gBACfpC,OAAA,CAACI,WAAW;MAAAgC,QAAA,gBACVpC,OAAA,CAACM,SAAS;QAAA8B,QAAA,GAAC,mBAET,eAAApC,OAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxC,OAAA,CAACQ,QAAQ;UAAA4B,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEZxC,OAAA,CAACW,UAAU;QAAAyB,QAAA,gBACTpC,OAAA;UAAAoC,QAAA,gBACEpC,OAAA,CAACa,YAAY;YAAAuB,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACjDxC,OAAA,CAACe,aAAa;YAAAqB,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNxC,OAAA;UAAKyC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAAN,QAAA,gBACjCpC,OAAA,CAACa,YAAY;YAAAuB,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACnDxC,OAAA,CAACe,aAAa;YAAAqB,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdxC,OAAA,CAACiB,QAAQ;MAAAmB,QAAA,EACNH,SAAS,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB7C,OAAA,CAACmB,QAAQ;QAAAiB,QAAA,gBACPpC,OAAA,CAACqB,QAAQ;UAACE,OAAO,EAAEqB,IAAI,CAACrB,OAAQ;UAAAa,QAAA,EAC7BQ,IAAI,CAACV;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACXxC,OAAA,CAACyB,QAAQ;UAAAW,QAAA,EAAEQ,IAAI,CAACT;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAChCxC,OAAA,CAAC2B,SAAS;UAAAS,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA,GALXK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMV,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEXxC,OAAA,CAAC6B,aAAa;MAAAO,QAAA,gBACZpC,OAAA;QAAAoC,QAAA,EAAM;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfxC,OAAA;QAAAoC,QAAA,EAAM;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEvB,CAAC;AAACM,IAAA,GAhDId,OAAO;AAkDb,eAAeA,OAAO;AAAC,IAAA7B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAe,IAAA;AAAAC,YAAA,CAAA5C,EAAA;AAAA4C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,IAAA;AAAAmB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}